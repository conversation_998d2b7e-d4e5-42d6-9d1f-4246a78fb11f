# 🚀 Configuración Rápida - MCP Firebird con ChatMCP

## ⚡ **Configuración Inmediata que Funciona**

### **📋 Configuración STDIO (Recomendada)**

Para conectar MCP Firebird con tu base de datos local, usa esta configuración exacta:

```
Nombre: MCP Firebird Local
Tipo de Conexión: STDIO
Path: npx
Arguments: mcp-firebird --database F:\Proyectos\SAI\AUTOSERVICIO_LA_PAZ.FDB --user SYSDBA --password masterkey
Variables de Entorno:
FIREBIRD_HOST=localhost
FIREBIRD_PORT=3050
FIREBIRD_USER=SYSDBA
FIREBIRD_PASSWORD=masterkey
LOG_LEVEL=info
```

### **🔧 Pasos Exactos en ChatMCP:**

1. **Abrir ChatMCP**: http://localhost:5173
2. **Hacer clic en "+"** en la barra lateral
3. **Llenar el formulario**:
   - **Nombre**: `MCP Firebird Local`
   - **Tipo de Conexión**: <PERSON>le<PERSON>onar `STDIO`
   - **Path**: `npx`
   - **Arguments**: `mcp-firebird --database F:\Proyectos\SAI\AUTOSERVICIO_LA_PAZ.FDB --user SYSDBA --password masterkey`
   - **Variables de Entorno** (una por línea):
     ```
     FIREBIRD_HOST=localhost
     FIREBIRD_PORT=3050
     FIREBIRD_USER=SYSDBA
     FIREBIRD_PASSWORD=masterkey
     LOG_LEVEL=info
     ```

4. **Hacer clic en "Add Server"**
5. **Verificar conexión**: Debe aparecer como "Conectado"

### **🎯 Configuración Alternativa (Si la primera no funciona)**

```
Nombre: MCP Firebird Alt
Tipo de Conexión: STDIO
Path: mcp-firebird
Arguments: --database F:\Proyectos\SAI\AUTOSERVICIO_LA_PAZ.FDB --user SYSDBA --password masterkey --host localhost --port 3050
Variables de Entorno:
LOG_LEVEL=debug
```

### **🔍 Verificación de Funcionamiento**

Una vez conectado, deberías ver estas herramientas disponibles:
- ✅ `execute-query`
- ✅ `list-tables`
- ✅ `describe-table`
- ✅ `get-field-descriptions`
- ✅ `analyze-query-performance`
- ✅ `backup-database`
- ✅ `validate-database`
- ✅ `get-methods`
- ✅ `describe-method`
- ✅ `ping`

### **🐛 Si No Funciona**

#### **Error: "The file argument must be of type string"**
- **Causa**: El campo Path está vacío
- **Solución**: Asegúrate de llenar el campo "Path" con `npx`

#### **Error: "Connection failed"**
- **Causa**: MCP Firebird no está instalado o la base de datos no es accesible
- **Solución**: 
  ```bash
  # Verificar instalación
  npx mcp-firebird --help
  
  # Si no está instalado
  npm install -g mcp-firebird
  ```

#### **Error: "Server not found"**
- **Causa**: Path incorrecto
- **Solución**: Usar `npx` en lugar de `mcp-firebird` directamente

### **📊 Prueba Rápida**

Una vez conectado, puedes probar con estos comandos en el chat:

1. **"Muéstrame todas las tablas"** - Debería listar las tablas de tu base de datos
2. **"Describe la tabla [NOMBRE_TABLA]"** - Mostrará la estructura de una tabla
3. **"Ejecuta SELECT COUNT(*) FROM [TABLA]"** - Contará registros

### **🎉 ¡Listo!**

Con esta configuración deberías poder conectar exitosamente MCP Firebird con ChatMCP y comenzar a interactuar con tu base de datos Firebird a través del chat.

---

**💡 Tip**: Si tienes problemas, revisa la consola del navegador (F12) para ver errores específicos y compártelos para obtener ayuda más precisa.
