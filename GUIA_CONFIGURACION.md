# 🔥 Guía Completa: Conectar MCP Firebird con ChatMCP

Esta guía te explica paso a paso cómo conectar el servidor **MCP Firebird** con tu base de datos local usando **ChatMCP**.

## 📋 **Prerrequisitos**

Antes de comenzar, asegúrate de tener:

1. **Node.js 18+** instalado
2. **Base de datos Firebird** funcionando localmente
3. **ChatMCP** ejecutándose (frontend + backend)
4. Credenciales de acceso a tu base de datos Firebird

## 🚀 **Paso 1: Instalar MCP Firebird**

### **Opción A: Instalación Automática (Recomendada)**
```bash
npx -y @smithery/cli install @PuroDelphi/mcpFirebird --client claude
```

### **Opción B: Instalación Manual**
```bash
# Instalación global
npm install -g mcp-firebird

# Verificar instalación
npx mcp-firebird --help
```

## ⚙️ **Paso 2: Configurar Variables de Entorno**

Crea un archivo `.env` o configura las variables de entorno:

```bash
# Configuración básica de Firebird
export FIREBIRD_HOST=localhost
export FIREBIRD_PORT=3050
export FIREBIRD_DATABASE=C:\path\to\tu\database.fdb  # Windows
# export FIREBIRD_DATABASE=/path/to/tu/database.fdb   # Linux/macOS
export FIREBIRD_USER=SYSDBA
export FIREBIRD_PASSWORD=masterkey
export LOG_LEVEL=info
```

## 🔧 **Paso 3: Configurar en ChatMCP**

### **3.1 Acceder a la Configuración**
1. Abre **ChatMCP** en tu navegador (`http://localhost:8080`)
2. Ve a la **barra lateral** y busca el botón **"+"** o **"Agregar Servidor"**
3. Selecciona **"Configurar Nuevo Servidor MCP"**

### **3.2 Configuración del Servidor**
Completa el formulario con estos datos:

```json
{
  "name": "Firebird Local",
  "connectionType": "stdio",
  "path": "npx",
  "args": [
    "mcp-firebird",
    "--database", "C:\\path\\to\\tu\\database.fdb",
    "--host", "localhost",
    "--port", "3050",
    "--user", "SYSDBA",
    "--password", "masterkey"
  ],
  "env": {
    "FIREBIRD_HOST": "localhost",
    "FIREBIRD_PORT": "3050",
    "FIREBIRD_USER": "SYSDBA",
    "FIREBIRD_PASSWORD": "masterkey",
    "LOG_LEVEL": "info"
  }
}
```

### **3.3 Configuración Alternativa con Archivo de Config**

Si prefieres usar un archivo de configuración:

1. Crea `firebird-config.js`:
```javascript
module.exports = {
  database: "C:\\path\\to\\tu\\database.fdb",
  user: "SYSDBA",
  password: "masterkey",
  host: "localhost",
  port: 3050,
  
  security: {
    // Tablas permitidas (opcional)
    allowedTables: ['CUSTOMERS', 'ORDERS', 'PRODUCTS'],
    
    // Operaciones permitidas
    allowedOperations: ['SELECT', 'INSERT', 'UPDATE'],
    
    // Límites de recursos
    resourceLimits: {
      maxRowsPerQuery: 1000,
      maxQueryCpuTime: 5000
    }
  }
};
```

2. Configurar en ChatMCP:
```json
{
  "name": "Firebird Local",
  "connectionType": "stdio", 
  "path": "npx",
  "args": [
    "mcp-firebird",
    "--config", "./firebird-config.js"
  ]
}
```

## ✅ **Paso 4: Verificar la Conexión**

### **4.1 Probar la Conexión**
1. Guarda la configuración en ChatMCP
2. El servidor debería aparecer como **"Conectado"** en la barra lateral
3. Verifica que aparezcan las **herramientas disponibles**

### **4.2 Herramientas Disponibles**
Una vez conectado, tendrás acceso a estas herramientas:

- **`execute-query`**: Ejecutar consultas SQL
- **`list-tables`**: Listar tablas de la base de datos
- **`describe-table`**: Obtener estructura de tablas
- **`get-field-descriptions`**: Descripciones de campos
- **`analyze-query-performance`**: Análisis de rendimiento
- **`backup-database`**: Crear respaldos
- **`validate-database`**: Validar integridad

## 🎯 **Paso 5: Uso Básico**

### **Ejemplo de Conversación**
Una vez configurado, puedes chatear así:

**Usuario:** "Muéstrame todas las tablas de mi base de datos"

**ChatMCP:** Usará la herramienta `list-tables` y te mostrará:
```
Tablas encontradas:
- CUSTOMERS
- ORDERS  
- PRODUCTS
- INVOICES
```

**Usuario:** "Describe la estructura de la tabla CUSTOMERS"

**ChatMCP:** Usará `describe-table` y mostrará:
```
Tabla: CUSTOMERS
- ID (INTEGER, Primary Key)
- NAME (VARCHAR(100), Not Null)
- EMAIL (VARCHAR(255))
- CREATED_DATE (TIMESTAMP)
```

**Usuario:** "Muéstrame los últimos 10 clientes registrados"

**ChatMCP:** Ejecutará:
```sql
SELECT FIRST 10 * FROM CUSTOMERS 
ORDER BY CREATED_DATE DESC
```

## 🔒 **Configuraciones de Seguridad**

### **Configuración Básica de Seguridad**
```javascript
// firebird-security-config.js
module.exports = {
  database: process.env.FIREBIRD_DATABASE,
  user: process.env.FIREBIRD_USER,
  password: process.env.FIREBIRD_PASSWORD,

  security: {
    // Solo permitir consultas SELECT
    allowedOperations: ['SELECT'],
    
    // Tablas específicas
    allowedTables: [
      'CUSTOMERS', 'ORDERS', 'PRODUCTS'
    ],
    
    // Excluir tablas sensibles
    forbiddenTables: [
      'SYSTEM_CONFIG', 'USER_PASSWORDS'
    ],
    
    // Enmascarar datos sensibles
    dataMasking: [
      {
        columns: ['CUSTOMER_EMAIL', 'PHONE'],
        pattern: /^.*/,
        replacement: '[REDACTED]'
      }
    ],
    
    // Límites de recursos
    resourceLimits: {
      maxRowsPerQuery: 1000,
      maxQueryCpuTime: 5000,
      queriesPerMinute: 60
    },
    
    // Auditoría
    audit: {
      enabled: true,
      destination: 'file',
      auditFile: './logs/firebird-audit.log'
    }
  }
};
```

## 🐛 **Solución de Problemas Comunes**

### **Error: "gbak not found"**
```bash
# Windows
set PATH=%PATH%;C:\Program Files\Firebird\Firebird_3_0\bin

# Linux/Ubuntu
sudo apt-get install firebird3.0-utils

# macOS
brew install firebird
```

### **Error de Conexión**
1. Verifica que Firebird esté ejecutándose
2. Confirma las credenciales
3. Revisa el path de la base de datos
4. Verifica permisos de archivo

### **Error de Permisos**
```sql
-- Otorgar permisos al usuario
GRANT SELECT, INSERT, UPDATE ON TABLE_NAME TO USER_NAME;
```

## 🔄 **Configuración con Docker (Avanzada)**

Si prefieres usar Docker para un entorno más controlado:

### **Docker Compose**
```yaml
# docker-compose.yml
version: '3.8'
services:
  firebird-db:
    image: jacobalberty/firebird:3.0
    environment:
      ISC_PASSWORD: masterkey
      FIREBIRD_DATABASE: database.fdb
    ports:
      - "3050:3050"
    volumes:
      - ./data:/firebird/data

  mcp-firebird:
    image: purodelphi/mcp-firebird
    environment:
      FIREBIRD_HOST: firebird-db
      FIREBIRD_PORT: 3050
      FIREBIRD_USER: SYSDBA
      FIREBIRD_PASSWORD: masterkey
      FIREBIRD_DATABASE: /firebird/data/database.fdb
    depends_on:
      - firebird-db
    stdin_open: true
    tty: true
```

### **Configuración en ChatMCP para Docker**
```json
{
  "name": "Firebird Docker",
  "connectionType": "stdio",
  "path": "docker",
  "args": [
    "run", "-i", "--rm",
    "--network", "chatmcp_mcp-network",
    "-e", "FIREBIRD_HOST=firebird-db",
    "-e", "FIREBIRD_PORT=3050",
    "-e", "FIREBIRD_USER=SYSDBA",
    "-e", "FIREBIRD_PASSWORD=masterkey",
    "-e", "FIREBIRD_DATABASE=/firebird/data/database.fdb",
    "purodelphi/mcp-firebird"
  ]
}
```

## 📝 **Resumen de Pasos**

1. ✅ **Instalar** MCP Firebird con npm/npx
2. ✅ **Configurar** variables de entorno
3. ✅ **Agregar servidor** en ChatMCP con configuración STDIO
4. ✅ **Verificar** conexión y herramientas disponibles
5. ✅ **Comenzar** a chatear con tu base de datos

¡Con esta configuración, podrás interactuar con tu base de datos Firebird de forma natural a través de ChatMCP! 🎉
