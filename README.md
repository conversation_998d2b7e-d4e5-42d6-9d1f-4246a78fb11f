# ChatMCP - Inicialización y Ejecución

## Requisitos previos
- Node.js 18+ y npm
- (Opcional) Variables de entorno para MCP servers (ejemplo: BRAVE_API_KEY)

## Instalación

```sh
npm install
```

## Ejecución

### 1. Iniciar el backend (API MCP Tools)

```sh
node mcp-tools-api.mjs
```

Esto levanta el endpoint en http://localhost:3001/api/mcp-tools para conectar con servidores MCP vía STDIO.

### 2. Iniciar el frontend (Vite + React)

En otra terminal:

```sh
npm run dev
```

Esto levanta la interfaz web en http://localhost:8080/

---

## Notas
- Si usas MCP servers que requieren variables de entorno (por ejemplo, Brave Search), asegúrate de que el frontend envíe correctamente el objeto `env` al backend.
- El backend y frontend pueden ejecutarse en paralelo.
- Si tienes errores de permisos o rutas, revisa el PATH de Node.js y la configuración de tu sistema.

---

# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/3bedf65a-0586-452d-b324-d4443bacae50

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/3bedf65a-0586-452d-b324-d4443bacae50) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/3bedf65a-0586-452d-b324-d4443bacae50) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
