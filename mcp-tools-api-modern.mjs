import express from 'express';
import cors from 'cors';
import { randomUUID } from 'node:crypto';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { WebSocketClientTransport } from '@modelcontextprotocol/sdk/client/websocket.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

const app = express();
app.use(cors());
app.use(express.json());

// Store active client sessions
const clientSessions = new Map();

// Enhanced client capabilities with new MCP features
const CLIENT_CAPABILITIES = {
  roots: {
    listChanged: true
  },
  sampling: {},
  experimental: {}
};

// Modern MCP client with backwards compatibility
async function createMCPClient(config) {
  const { path, args = [], env = {}, transport = 'stdio', url } = config;
  
  const client = new Client(
    { 
      name: 'chatMCP-modern-backend', 
      version: '2.0.0' 
    },
    { 
      capabilities: CLIENT_CAPABILITIES
    }
  );

  let clientTransport;

  try {
    switch (transport) {
      case 'websocket':
        if (!url) throw new Error('URL required for WebSocket transport');
        clientTransport = new WebSocketClientTransport(new URL(url));
        console.log('[MCP][MODERN] Using WebSocket transport');
        break;

      case 'sse':
        if (!url) throw new Error('URL required for SSE transport');
        clientTransport = new SSEClientTransport(new URL(url));
        console.log('[MCP][MODERN] Using SSE transport');
        break;

      case 'stdio':
      default:
        clientTransport = new StdioClientTransport({
          command: path,
          args,
          env: { ...process.env, ...env },
        });
        console.log('[MCP][MODERN] Using STDIO transport');
        break;
    }

    await client.connect(clientTransport);
    return { client, transport: clientTransport };
    
  } catch (error) {
    console.error('[MCP][MODERN] Transport connection failed:', error);
    throw error;
  }
}

// Enhanced endpoint with modern MCP features
app.post('/api/mcp-tools', async (req, res) => {
  const sessionId = req.headers['mcp-session-id'] || randomUUID();
  const { path, args = [], env = {}, transport = 'stdio', url } = req.body;
  
  try {
    console.log('[MCP][MODERN] Creating client session:', sessionId);
    
    const { client, transport: clientTransport } = await createMCPClient({
      path, args, env, transport, url
    });

    // Store session for potential reuse
    clientSessions.set(sessionId, { client, transport: clientTransport });

    // Get comprehensive server information
    const [tools, resources, prompts] = await Promise.all([
      client.listTools().catch(() => ({ tools: [] })),
      client.listResources().catch(() => ({ resources: [] })),
      client.listPrompts().catch(() => ({ prompts: [] }))
    ]);

    const response = {
      sessionId,
      capabilities: CLIENT_CAPABILITIES,
      server: {
        tools: tools.tools || [],
        resources: resources.resources || [],
        prompts: prompts.prompts || []
      },
      transport: transport,
      protocolVersion: '2025-03-26'
    };

    console.log('[MCP][MODERN] Session created successfully:', sessionId);
    res.json(response);

  } catch (err) {
    console.error('[MCP][MODERN] Error:', err);
    res.status(500).json({ 
      error: err.message, 
      stack: err.stack,
      sessionId 
    });
  }
});

// New endpoint for tool execution with session management
app.post('/api/mcp-tools/:sessionId/call', async (req, res) => {
  const { sessionId } = req.params;
  const { toolName, arguments: toolArgs, progressToken } = req.body;

  try {
    const session = clientSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const { client } = session;

    // Enhanced tool call with progress support
    const requestParams = {
      name: toolName,
      arguments: toolArgs || {}
    };

    // Add progress token if provided
    if (progressToken) {
      requestParams._meta = { progressToken };
    }

    const result = await client.callTool(requestParams);
    
    res.json({
      sessionId,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    console.error('[MCP][MODERN] Tool call error:', err);
    res.status(500).json({ 
      error: err.message,
      sessionId 
    });
  }
});

// New endpoint for resource reading
app.post('/api/mcp-resources/:sessionId/read', async (req, res) => {
  const { sessionId } = req.params;
  const { uri } = req.body;

  try {
    const session = clientSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const { client } = session;
    const result = await client.readResource({ uri });
    
    res.json({
      sessionId,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    console.error('[MCP][MODERN] Resource read error:', err);
    res.status(500).json({ 
      error: err.message,
      sessionId 
    });
  }
});

// New endpoint for prompt execution
app.post('/api/mcp-prompts/:sessionId/get', async (req, res) => {
  const { sessionId } = req.params;
  const { name, arguments: promptArgs } = req.body;

  try {
    const session = clientSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const { client } = session;
    const result = await client.getPrompt({ 
      name, 
      arguments: promptArgs || {} 
    });
    
    res.json({
      sessionId,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    console.error('[MCP][MODERN] Prompt get error:', err);
    res.status(500).json({ 
      error: err.message,
      sessionId 
    });
  }
});

// Session management endpoints
app.delete('/api/mcp-sessions/:sessionId', async (req, res) => {
  const { sessionId } = req.params;
  
  try {
    const session = clientSessions.get(sessionId);
    if (session) {
      await session.client.disconnect();
      clientSessions.delete(sessionId);
    }
    
    res.json({ 
      message: 'Session terminated',
      sessionId 
    });

  } catch (err) {
    console.error('[MCP][MODERN] Session termination error:', err);
    res.status(500).json({ 
      error: err.message,
      sessionId 
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    version: '2.0.0',
    protocol: '2025-03-26',
    activeSessions: clientSessions.size,
    capabilities: CLIENT_CAPABILITIES,
    timestamp: new Date().toISOString()
  });
});

// Backwards compatibility endpoint
app.post('/api/mcp-tools-legacy', async (req, res) => {
  console.log('[MCP][LEGACY] Using legacy endpoint');
  // Redirect to modern endpoint
  req.url = '/api/mcp-tools';
  return app._router.handle(req, res);
});

// Cleanup inactive sessions periodically
setInterval(() => {
  const now = Date.now();
  for (const [sessionId, session] of clientSessions.entries()) {
    // Clean up sessions older than 1 hour
    if (now - session.createdAt > 3600000) {
      session.client.disconnect().catch(console.error);
      clientSessions.delete(sessionId);
      console.log('[MCP][CLEANUP] Removed inactive session:', sessionId);
    }
  }
}, 300000); // Check every 5 minutes

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 MCP Modern Tools API running on port ${PORT}`);
  console.log(`📋 Protocol version: 2025-03-26`);
  console.log(`🔧 Capabilities: ${Object.keys(CLIENT_CAPABILITIES).join(', ')}`);
});
