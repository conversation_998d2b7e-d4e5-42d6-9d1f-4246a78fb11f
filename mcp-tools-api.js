const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');

const app = express();
app.use(cors());
app.use(express.json());

// Endpoint para listar herramientas de un server MCP tipo STDIO
app.post('/api/mcp-tools', async (req, res) => {
  const { path, args } = req.body;
  if (!path) {
    return res.status(400).json({ error: 'Missing path to MCP server executable' });
  }
  try {
    const proc = spawn(path, args || [], { stdio: ['pipe', 'pipe', 'pipe'] });
    let stdout = '';
    let stderr = '';

    proc.stdin.write('list_tools\n');
    proc.stdin.end();

    proc.stdout.on('data', chunk => { stdout += chunk.toString(); });
    proc.stderr.on('data', chunk => { stderr += chunk.toString(); });

    proc.on('close', code => {
      if (stderr) {
        return res.status(500).json({ error: 'STDERR: ' + stderr });
      }
      try {
        const toolsJson = JSON.parse(stdout);
        res.json(toolsJson);
      } catch (e) {
        res.status(500).json({ error: 'Invalid JSON from MCP server', raw: stdout });
      }
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => console.log(`MCP Tools API running on port ${PORT}`));
