import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';

// DEBUG: Mostrar el PATH actual
console.log('PATH antes:', process.env.PATH);

// Forzar la inclusión de la ruta de nodejs al PATH si no está
const nodePath = 'C\\Program Files\\nodejs';
if (!process.env.PATH.includes(nodePath)) {
  process.env.PATH += `;${nodePath}`;
  console.log('PATH modificado:', process.env.PATH);
}

const app = express();
app.use(cors());
app.use(express.json());

// Endpoint para listar herramientas de un server MCP tipo STDIO
app.post('/api/mcp-tools', async (req, res) => {
  const { path, args = [], env = {} } = req.body;

  // Validate that path is provided
  if (!path) {
    console.error('[MCP][API] Error: No path provided');
    return res.status(400).json({ error: 'Path is required for STDIO connection' });
  }

  try {
    console.log('[MCP][API] Launching MCP server:', path, args, env);
    const clientModule = await import('@modelcontextprotocol/sdk/client/index.js');
    const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js');
    console.log('[DEBUG] clientModule keys:', Object.keys(clientModule));
    console.log('[DEBUG] stdioModule keys:', Object.keys(stdioModule));
    const { Client } = clientModule;
    const { StdioClientTransport } = stdioModule;
    const client = new Client(
      { name: 'chatMCP-backend', version: '1.0.0' },
      { capabilities: { tools: {}, resources: {}, prompts: {} } }
    );
    const transport = new StdioClientTransport({
      command: path,
      args,
      env: { ...process.env, ...env },
    });
    await client.connect(transport);
    console.log('[DEBUG] typeof Client:', typeof Client);
    console.log('[DEBUG] Client prototype:', Object.getOwnPropertyNames(Client.prototype));
    // await client.initialize();
    const tools = await client.listTools();
    console.log('[MCP][API] Tools received:', tools);
    res.json(tools);
    await client.disconnect();
  } catch (err) {
    console.error('[MCP][API] Error:', err);
    res.status(500).json({ error: err.message, stack: err.stack });
  }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => console.log(`MCP Tools API running on port ${PORT}`));
