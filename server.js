// Backend Express para invocación MCP STDIO segura
import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';

const app = express();
const PORT = process.env.PORT_API || 3010;

app.use(cors());
app.use(express.json());

/**
 * POST /api/mcp-stdio
 * Body: { path: string, args?: string[], input: { tool: string, args: object } }
 * Ejecuta un proceso stdio y responde el resultado (JSON o texto)
 */
app.post('/api/mcp-stdio', async (req, res) => {
  const { path, args = [], input } = req.body;
  if (!path || !input) {
    return res.status(400).json({ error: 'Missing path or input' });
  }
  try {
    // Resolución automática de npx/npx.cmd si el path es 'npx'
    let resolvedPath = path;
    if (path === 'npx') {
      const { execSync } = await import('node:child_process');
      try {
        if (process.platform === 'win32') {
          const fs = await import('node:fs');
          const foundList = execSync('where npx.cmd', { encoding: 'utf-8' }).split(/\r?\n/).map(s => s.trim()).filter(Boolean);
          const usable = foundList.find(f => fs.existsSync(f));
          if (usable) {
            resolvedPath = usable;
          } else {
            throw new Error('No se encontró npx.cmd utilizable en el PATH. Rutas probadas: ' + foundList.join(', '));
          }
        } else {
          const found = execSync('which npx', { encoding: 'utf-8' }).split(/\r?\n/).find(Boolean);
          if (found) {
            resolvedPath = found.trim();
          } else {
            throw new Error('No se encontró npx en el PATH.');
          }
        }
      } catch (err) {
        console.error('[STDIO][API] No se pudo resolver la ruta de npx:', err);
        throw new Error('No se pudo encontrar npx/npx.cmd en el PATH del sistema.');
      }
    }
    console.log('[STDIO][API] resolvedPath:', resolvedPath, '| typeof:', typeof resolvedPath);
    console.log('[STDIO][API] args:', args, '| typeof:', typeof args);
    if (Array.isArray(args)) {
      args.forEach((arg, idx) => {
        console.log(`[STDIO][API] args[${idx}]:`, arg, '| typeof:', typeof arg);
      });
    }
    console.log('[STDIO][API] process.env.PATH:', process.env.PATH);
    let proc;
    let spawnPath = resolvedPath;
    if (process.platform === 'win32' && resolvedPath.includes(' ')) {
      spawnPath = '"' + resolvedPath + '"';
    }
    try {
      proc = spawn(spawnPath, args, { stdio: ['pipe', 'pipe', 'pipe'], shell: process.platform === 'win32' });
    } catch (spawnErr) {
      console.error('[STDIO][API] Error en spawn:', spawnErr, '\nresolvedPath:', resolvedPath, '\nargs:', args);
      throw spawnErr;
    }
    let responded = false;
    let requestId = Math.floor(Math.random() * 1e9);
    let buffer = '';
    let errorBuffer = '';
    let toolRequested = input && input.tool;
    let paramsRequested = (input && input.args) || {};
    let jsonrpcRequest = null;
    // Helper async para spawn listTools con import ESM
async function spawnListToolsESM(path, args, listToolsReq, toolRequested, res, listToolsId) {
  const { spawn } = await import('child_process');
  const proc2 = spawn(path, args, { stdio: ['pipe', 'pipe', 'pipe'], shell: process.platform === 'win32' });
  let out2 = '';
  proc2.stdout.on('data', (d) => { out2 += d.toString(); });
  proc2.on('close', () => {
    const lines2 = out2.split('\n').map(l => l.trim()).filter(Boolean);
    let found = false;
    for (const l of lines2) {
      try {
        const j = JSON.parse(l);
        if (j.jsonrpc === '2.0' && j.id === listToolsId && j.result && Array.isArray(j.result.tools)) {
          found = true;
          const available = j.result.tools.map(t => t.name);
          console.error('[STDIO][API][DEBUG] Métodos disponibles según listTools:', available);
          if (!available.includes(toolRequested)) {
            return res.status(500).json({ error: `El método '${toolRequested}' no está disponible en el servidor MCP. Métodos: ` + available.join(', ') });
          } else {
            return res.status(500).json({ error: `El método '${toolRequested}' existe pero fue rechazado por el servidor. Puede requerir parámetros distintos o contexto.` });
          }
        }
      } catch {}
    }
    if (!found) {
      return res.status(500).json({ error: `No se pudo obtener lista de métodos tras error Method Not Found. Output: ` + out2 });
    }
  });
  proc2.stdin.write(listToolsReq);
  proc2.stdin.end();
}

// Procesar stdout línea a línea y buscar respuesta JSON-RPC válida
    proc.stdout.on('data', (data) => {
      buffer += data.toString();
      let lines = buffer.split('\n');
      buffer = lines.pop() || '';
      for (const line of lines) {
        let msg;
        try {
          msg = JSON.parse(line);
        } catch { continue; }
        // Log all JSON-RPC messages for debug
        if (msg && msg.jsonrpc === '2.0') {
          console.log('[STDIO][API][JSON-RPC] Recibido:', msg);
        }
        if (msg && msg.jsonrpc === '2.0' && msg.id === requestId) {
          if (!responded) {
            responded = true;
            if ('result' in msg) {
              return res.json({ result: msg.result });
            } else if ('error' in msg) {
              // Si error es Method Not Found, intentar listTools
              if (msg.error && msg.error.code === -32601) {
                // Lanzar un proceso para consultar listTools usando un helper async compatible ESM
                const listToolsId = requestId + 1;
                const listToolsReq = JSON.stringify({
                  jsonrpc: '2.0',
                  id: listToolsId,
                  method: 'listTools',
                  params: {}
                }) + '\n';
                console.log('[STDIO][API] Intentando listTools tras Method Not Found:', listToolsReq);
                // Llama helper async para spawn
                spawnListToolsESM(path, args, listToolsReq, toolRequested, res, listToolsId);
                return;
              }
              return res.status(500).json({ error: msg.error });
            }
          }
        }
      }
    });
    proc.stderr.on('data', (data) => { errorBuffer += data.toString(); });
    proc.on('error', (err) => {
      console.error(`[STDIO][API] Error spawning process (${path}):`, err);
      if (!responded) {
        responded = true;
        res.status(500).json({ error: 'Failed to spawn process', details: String(err) });
      }
    });
    proc.on('close', (code) => {
      if (!responded) {
        responded = true;
        res.status(500).json({ error: 'No JSON-RPC response received', code, stdout: buffer, stderr: errorBuffer });
      }
    });
    // --- Enviar comando MCP por stdin (protocolo MCP-STDIO JSON-RPC 2.0) ---
    if (input && input.tool) {
      jsonrpcRequest = JSON.stringify({
        jsonrpc: '2.0',
        id: requestId,
        method: input.tool,
        params: input.args || {}
      }) + '\n';
      console.log('[STDIO][API] Enviando a stdin:', jsonrpcRequest);
      proc.stdin.write(jsonrpcRequest);
      proc.stdin.end();
    }
  } catch (err) {
    res.status(500).json({ error: 'Exception running stdio tool', details: String(err) });
  }
});

// Endpoint robusto para listar herramientas de un server MCP tipo STDIO
app.all('/api/mcp-tools', async (req, res) => {
  console.log(`[MCP][API] /api/mcp-tools llamado con método ${req.method}`);
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed. Use POST.' });
  }
  const { path, args = [], env = {} } = req.body || {};
  if (!path) {
    console.warn('[MCP][API] Faltan datos: path es requerido');
    return res.status(400).json({ error: 'Missing "path" in request body.' });
  }
  try {
    let clientModule, stdioModule;
    try {
      clientModule = await import('@modelcontextprotocol/sdk/client/index.js');
      stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js');
    } catch (sdkErr) {
      console.error('[MCP][API] MCP SDK not installed:', sdkErr);
      return res.status(500).json({ error: 'MCP SDK not installed', details: sdkErr.message });
    }
    const { Client } = clientModule;
    const { StdioClientTransport } = stdioModule;
    const client = new Client(
      { name: 'chatMCP-backend', version: '1.0.0' },
      { capabilities: { tools: {}, resources: {}, prompts: {} } }
    );
    // Si el path es 'npx', busca la ruta absoluta automáticamente
    let resolvedCommand = path;
    if (path === 'npx') {
      const { execSync } = await import('node:child_process');
      try {
        if (process.platform === 'win32') {
          // Busca npx.cmd en el PATH
          const fs = await import('node:fs');
          const foundList = execSync('where npx.cmd', { encoding: 'utf-8' }).split(/\r?\n/).map(s => s.trim()).filter(Boolean);
          const usable = foundList.find(f => fs.existsSync(f));
          if (usable) {
            resolvedCommand = usable;
          } else {
            throw new Error('No se encontró npx.cmd utilizable en el PATH. Rutas probadas: ' + foundList.join(', '));
          }
        } else {
          // Busca npx en sistemas tipo Unix
          const found = execSync('which npx', { encoding: 'utf-8' }).split(/\r?\n/).find(Boolean);
          if (found) {
            resolvedCommand = found.trim();
          } else {
            throw new Error('No se encontró npx en el PATH.');
          }
        }
      } catch (err) {
        console.error('[MCP][API] No se pudo resolver la ruta de npx:', err);
        throw new Error('No se pudo encontrar npx/npx.cmd en el PATH del sistema.');
      }
    }
    const transport = new StdioClientTransport({
      command: resolvedCommand,
      args,
      env: { ...process.env, ...env },
    });
    await client.connect(transport);
    const toolsRaw = await client.listTools();
    console.log(`[MCP][API] Herramientas obtenidas para path=${path}:`, toolsRaw);
    // Normaliza la respuesta para que siempre sea { tools: [...] }
    let tools = [];
    if (Array.isArray(toolsRaw)) {
      tools = toolsRaw;
    } else if (toolsRaw && Array.isArray(toolsRaw.tools)) {
      tools = toolsRaw.tools;
    } else {
      console.warn('[MCP][API] Respuesta inesperada de listTools, no es array ni objeto con tools:', toolsRaw);
    }
    res.json({ tools });
    // NO llames a client.disconnect() si no existe (previene crash)
    if (typeof client.disconnect === 'function') {
      await client.disconnect();
    }
  } catch (err) {
    console.error('[MCP][API] Error general /api/mcp-tools:', err);
    res.status(500).json({ error: err.message, stack: err.stack });
  }
});

app.listen(PORT, () => {
  console.log(`MCP STDIO API listening on port ${PORT}`);
});
