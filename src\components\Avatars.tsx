
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import { BrainIcon, User2Icon, WrenchIcon, BellIcon } from "lucide-react";

interface AvatarProps extends HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function UserAvatar({ className, ...props }: AvatarProps) {
  return (
    <Avatar className={cn("h-8 w-8 border border-primary/50", className)} {...props}>
      <AvatarFallback className="bg-accent">
        <User2Icon className="h-5 w-5 text-accent-foreground" />
      </AvatarFallback>
    </Avatar>
  );
}

export function BotAvatar({ className, ...props }: AvatarProps) {
  return (
    <Avatar className={cn("h-8 w-8 border border-primary/50", className)} {...props}>
      <AvatarFallback className="bg-primary/10">
        <BrainIcon className="h-5 w-5 text-primary" />
      </AvatarFallback>
    </Avatar>
  );
}

export function SystemAvatar({ className, ...props }: AvatarProps) {
  return (
    <Avatar className={cn("h-8 w-8 border border-muted/50", className)} {...props}>
      <AvatarFallback className="bg-muted/20">
        <BellIcon className="h-5 w-5 text-muted-foreground" />
      </AvatarFallback>
    </Avatar>
  );
}

export function ToolAvatar({ className, ...props }: AvatarProps) {
  return (
    <Avatar className={cn("h-8 w-8 border border-accent/50", className)} {...props}>
      <AvatarFallback className="bg-accent/20">
        <WrenchIcon className="h-5 w-5 text-accent-foreground" />
      </AvatarFallback>
    </Avatar>
  );
}
