
import { useState, useRef, useEffect } from "react";
import { invokeMCPTool } from "@/lib/invoke-mcp-tool";
import { useMCP } from "@/context/MCPContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { SendIcon, PaperclipIcon } from "lucide-react";
import { ChatMessage } from "@/types/mcp";
import { MessageItem } from "@/components/MessageItem";
import { ModelSelect } from "@/components/ModelSelect";
import { toast } from "sonner";

export function ChatInterface() {
  const {
    activeConversation,
    activeServers,
    activeModel,
    createConversation,
    sendMessage,
    setActiveModel,
    getProviderById,
    tools
  } = useMCP();
  
  const [messageInput, setMessageInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [typing, setTyping] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  
  // Auto-scroll to the bottom when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollArea = scrollAreaRef.current;
      scrollArea.scrollTop = scrollArea.scrollHeight;
    }
  }, [activeConversation?.messages]);
  
  const handleSendMessage = async () => {
    if (!messageInput.trim() || isLoading) return;

    // Check if we have a model selected
    if (!activeModel) {
      toast.error("Debes seleccionar un modelo antes de enviar un mensaje en el chat.");
      return;
    }

    // Check if we have any active servers
    if (activeServers.length === 0) {
      toast.error("No hay servidores activos. Activa al menos un servidor MCP desde el panel lateral.");
      return;
    }

    // If no conversation exists, create one
    if (!activeConversation) {
      createConversation(activeModel.id);
    }

    setIsLoading(true);
    setTyping(true);
    const message = messageInput;
    setMessageInput("");

    // Construir tools en formato OpenAI (corrigiendo el mapeo de parámetros)
    const toolsForPayload = tools.map(t => {
      // Construir el objeto properties correctamente
      const properties: Record<string, any> = {};
      (t.parameters || []).forEach(p => {
        let paramSchema: Record<string, any> = {
          type: p.type,
          description: p.description
        };
        if (p.type === 'array') {
          paramSchema.items = { type: 'string' }; // Puedes mejorar esto si tienes más info
        }
        if (p.default !== undefined) {
          paramSchema.default = p.default;
        }
        properties[p.name] = paramSchema;
      });
      return {
        type: "function",
        name: t.name,
        description: t.description,
        parameters: {
          type: "object",
          properties,
          required: (t.parameters || []).filter(p => p.required).map(p => p.name),
          additionalProperties: false
        },
        strict: true
      };
    });

    // Construir payload
    const payload = {
      model: activeModel.modelId || "gpt-4.1",
      input: [
        {
          role: "system",
          content: [
            {
              type: "input_text",
              text: "Eres un experto en SQL, Contador Público, Programador  y un muy amable asistente de soporte sobre el producto SAI Open.\n\nLos usuarios te harán preguntas que normalmente podrás responder utilizando las herramientas que te dan para ejecutar.\n\nResponde siempre de manera amable y profesional."
            }
          ]
        },
        {
          role: "user",
          content: [
            {
              type: "input_text",
              text: message
            }
          ]
        }
      ],
      text: {
        format: {
          type: "text"
        }
      },
      reasoning: {},
      tools: toolsForPayload,
      temperature: 0.5,
      max_output_tokens: 2048,
      top_p: 1,
      store: true
    };

    try {
      // 1. Agregar mensaje del usuario al chat
      let conversationMessages = activeConversation ? [...activeConversation.messages] : [];
      const userMsg = {
        id: `user_${Date.now()}`,
        role: "user",
        content: message,
        timestamp: Date.now()
      };
      conversationMessages.push(userMsg);

      // 2. Enviar al webhook externo y procesar respuesta
      const webhookResponse = await fetch("https://panel.asistentesautonomos.com/webhook/6f08dbcf-5888-4003-a386-766deb9976c8", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      });
      if (!webhookResponse.ok) throw new Error("Webhook externo respondió con error");
      const responseData = await webhookResponse.json();

      // Permite que la respuesta pueda ser un array o un solo objeto
      const responses = Array.isArray(responseData) ? responseData : [responseData];

      // Procesar cada objeto response (puede tener output[])
      // Procesar cada objeto response (puede tener output[])
      let inputSteps: any[] = [];
      let lastFunctionCall: any = null;
      for (const resp of responses) {
        if (!resp.output || !Array.isArray(resp.output)) continue;
        for (const out of resp.output) {
          if (out.type === "message" && out.role === "assistant") {
            // Mensaje normal del asistente
            const text = Array.isArray(out.content) && out.content.length > 0 && out.content[0].text
              ? out.content[0].text
              : "";
            conversationMessages.push({
              id: out.id || `assistant_${Date.now()}`,
              role: "assistant",
              content: text,
              timestamp: Date.now()
            });
            inputSteps.push({
              id: out.id || undefined,
              role: "assistant",
              content: out.content
            });
          } else if (out.type === "function_call" && out.status === "completed") {
            // Mostrar mensaje de tool_call diferenciado
            const toolCallMsg = {
              id: out.id,
              role: "tool",
              content: `Ejecutando herramienta: ${out.name}\nArgumentos: ${out.arguments}`,
              timestamp: Date.now(),
              toolCalls: [
                {
                  id: out.id,
                  toolId: out.name,
                  params: JSON.parse(out.arguments),
                  status: "pending"
                }
              ]
            };
            conversationMessages.push(toolCallMsg);
            // Para el request OpenAI, guardar el step function_call
            lastFunctionCall = {
              type: "function_call",
              call_id: out.call_id || out.id,
              name: out.name,
              arguments: out.arguments
            };
            inputSteps.push(lastFunctionCall);
            // Buscar herramienta y server correspondiente
            const tool = tools.find(t => t.name === out.name);
            if (!tool) {
              toast.error(`Herramienta ${out.name} no encontrada en MCP`);
              continue;
            }
            const server = activeServers.find(s => s.id === tool.serverId);
            if (!server) {
              toast.error(`Servidor para la herramienta ${out.name} no está activo`);
              continue;
            }
            // Ejecutar herramienta real con robustez y logging
            try {
              // Importación dinámica para evitar ciclos
              const { invokeMCPTool } = await import("@/lib/invoke-mcp-tool");
              const execResult = await invokeMCPTool({
                toolName: out.name,
                args: JSON.parse(out.arguments),
                tools,
                servers: activeServers
              });
              // Mostrar resultado en el chat
              const toolResultMsg = {
                id: `${out.id}_result`,
                role: "tool",
                content: `Resultado de ${out.name}: ${JSON.stringify(execResult)}`,
                timestamp: Date.now(),
                toolResults: [
                  {
                    toolCallId: out.id,
                    result: execResult
                  }
                ]
              };
              conversationMessages.push(toolResultMsg);
              // Agregar function_call_output al flujo input[]
              inputSteps.push({
                type: "function_call_output",
                call_id: out.call_id || out.id,
                output: JSON.stringify(execResult)
              });
            } catch (err) {
              const errorMsg = err instanceof Error ? err.message : String(err);
              conversationMessages.push({
                id: `${out.id}_error`,
                role: "tool",
                content: `Error ejecutando ${out.name}: ${errorMsg}`,
                timestamp: Date.now()
              });
              inputSteps.push({
                type: "function_call_output",
                call_id: out.call_id || out.id,
                output: JSON.stringify({ error: errorMsg })
              });
            }
          }
        }
      }

      // Si hay function_call_output, reenviar al webhook para multi-turno (formato OpenAI)
      if (inputSteps.some(step => step.type === "function_call_output")) {
        setTyping(true);
        // Construir el flujo input[] completo
        // Incluir system, user, y todos los steps previos (assistant, function_call, function_call_output)
        const systemStep = {
          role: "system",
          content: [
            {
              type: "input_text",
              text: "Eres un experto en SQL, Contador Público, Programador  y un muy amable asistente de soporte sobre el producto SAI Open.\n\nLos usuarios te harán preguntas que normalmente podrás responder utilizando las herramientas que te dan para ejecutar.\n\nResponde siempre de manera amable y profesional."
            }
          ]
        };
        const userStep = {
          role: "user",
          content: [
            {
              type: "input_text",
              text: message
            }
          ]
        };
        const inputArray = [systemStep, userStep, ...inputSteps];
        const followupPayload = {
          model: activeModel.modelId || "gpt-4.1",
          input: inputArray,
          text: {
            format: {
              type: "text"
            }
          },
          reasoning: {},
          tools: tools.map(t => {
            // Si no hay parámetros definidos, poner un objeto vacío válido
            let parameters: any;
            if (t.parameters && t.parameters.length > 0) {
              parameters = {
                type: "object",
                required: t.parameters.filter(p => p.required).map(p => p.name),
                properties: Object.fromEntries(
                  t.parameters.map(p => [p.name, {
                    type: p.type,
                    ...(p.type === 'array' ? { items: { type: 'string' } } : {}),
                    description: p.description,
                    ...(p.default !== undefined ? { default: p.default } : {})
                  }])
                ),
                additionalProperties: false
              };
            } else {
              parameters = {
                type: "object",
                properties: {},
                required: [],
                additionalProperties: false
              };
            }
            return {
              type: "function",
              name: t.name,
              strict: true,
              parameters,
              description: t.description
            };
          }),
          temperature: 0.5,
          max_output_tokens: 2048,
          top_p: 1,
          store: true
        };
        const followupRes = await fetch("https://panel.asistentesautonomos.com/webhook/6f08dbcf-5888-4003-a386-766deb9976c8", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(followupPayload)
        });
        if (followupRes.ok) {
          const followupData = await followupRes.json();
          // Procesar igual que arriba (puede ser array o objeto)
          const followupResponses = Array.isArray(followupData) ? followupData : [followupData];
          let retriedIncomplete = false;
          for (const resp of followupResponses) {
            // Manejo de respuesta incompleta por max_output_tokens
            if (resp.status === "incomplete" && resp.incomplete_details && resp.incomplete_details.reason === "max_output_tokens") {
              toast.warning("La respuesta fue truncada por límite de tokens. Se eliminarán mensajes antiguos y se reintentará automáticamente.");
              if (!retriedIncomplete) {
                retriedIncomplete = true;
                // Eliminar los mensajes más antiguos del historial (excepto system y el último user)
                const systemMsg = inputArray[0];
                const userMsg = inputArray[inputArray.length - 1];
                // Mantener solo system y último user
                const reducedInput = [systemMsg, userMsg, ...inputSteps.filter(step => step.type === "function_call" || step.type === "function_call_output")];
                const retryPayload = {
                  ...followupPayload,
                  input: reducedInput
                };
                const retryRes = await fetch("https://panel.asistentesautonomos.com/webhook/6f08dbcf-5888-4003-a386-766deb9976c8", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(retryPayload)
                });
                if (retryRes.ok) {
                  const retryData = await retryRes.json();
                  const retryResponses = Array.isArray(retryData) ? retryData : [retryData];
                  for (const rResp of retryResponses) {
                    if (!rResp.output || !Array.isArray(rResp.output)) continue;
                    for (const out of rResp.output) {
                      if (out.type === "message" && out.role === "assistant") {
                        const text = Array.isArray(out.content) && out.content.length > 0 && out.content[0].text
                          ? out.content[0].text
                          : "";
                        conversationMessages.push({
                          id: out.id || `assistant_${Date.now()}`,
                          role: "assistant",
                          content: text,
                          timestamp: Date.now()
                        });
                      }
                    }
                  }
                } else {
                  toast.error("Error al reintentar la petición tras truncamiento de tokens.");
                }
              }
              continue;
            }
            if (!resp.output || !Array.isArray(resp.output)) continue;
            for (const out of resp.output) {
              if (out.type === "message" && out.role === "assistant") {
                const text = Array.isArray(out.content) && out.content.length > 0 && out.content[0].text
                  ? out.content[0].text
                  : "";
                conversationMessages.push({
                  id: out.id || `assistant_${Date.now()}`,
                  role: "assistant",
                  content: text,
                  timestamp: Date.now()
                });
              }
            }
          }
        }
        setTyping(false);
      }
      // Actualizar conversación
      if (activeConversation) {
        activeConversation.messages = conversationMessages;
      }
      setIsLoading(false);
      setTyping(false);
    } catch (error) {
      console.error("Error en el flujo de herramientas MCP:", error);
      toast.error("Error procesando respuesta y herramientas");
      setIsLoading(false);
      setTyping(false);
    }
  };

  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  if (activeServers.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <div className="text-center glass p-8 rounded-xl slide-in-up">
          <img 
            src="/logo.png" 
            alt="Grupo SAI Logo" 
            className="h-16 mx-auto mb-6 float"
          />
          <h2 className="text-2xl font-semibold mb-2 text-primary">No Active Servers</h2>
          <p className="text-muted-foreground">
            Please activate at least one server from the sidebar to start a conversation.
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col h-full relative">
      {/* Background decorative elements */}
      <div className="absolute -top-20 -right-20 w-80 h-80 bg-primary/5 rounded-full blur-2xl"></div>
      <div className="absolute -bottom-40 -left-20 w-60 h-60 bg-primary/3 rounded-full blur-xl"></div>
      
      {/* Header */}
      <div className="flex items-center justify-between border-b p-4 bg-background/80 backdrop-blur-sm z-10">
        <div className="slide-in-right">
          <h2 className="font-semibold text-primary">
            {activeConversation ? activeConversation.title : "New Conversation"}
          </h2>
          <p className="text-sm text-muted-foreground">
            {activeServers.length} active MCP {activeServers.length === 1 ? 'server' : 'servers'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <ModelSelect />
        </div>
      </div>
      
      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4 scroll-smooth max-h-[calc(100vh-200px)] min-h-[200px] overflow-y-auto" ref={scrollAreaRef}>
        {activeConversation?.messages.length ? (
          activeConversation.messages.map((message) => (
            <MessageItem key={message.id} message={message} />
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="glass p-8 rounded-xl max-w-md slide-in-up">
              <img 
                src="/logo.png" 
                alt="Grupo SAI Logo" 
                className="h-20 mx-auto mb-6 float"
              />
              <h3 className="text-xl font-semibold text-primary">Start a new conversation</h3>
              <p className="text-muted-foreground mt-2">
                {activeModel 
                  ? "Type your message below to start chatting." 
                  : "Select a model from the dropdown above and start chatting."}
              </p>
              {activeServers.length === 0 ? (
                <p className="text-amber-500 mt-2">Please activate a server to continue.</p>
              ) : null}
            </div>
          </div>
        )}
        
        {typing && (
          <div className="flex items-start gap-3 py-4">
            <div className="flex-1 space-y-2 p-4 rounded-lg bg-primary/5 hover-glow">
              <div className="h-6 flex items-center">
                <div className="typing-indicator">
                  <span className="dot"></span>
                  <span className="dot"></span>
                  <span className="dot"></span>
                </div>
              </div>
            </div>
          </div>
        )}
      </ScrollArea>
      
      {/* Input Area */}
      <div className="border-t p-4 bg-background/80 backdrop-blur-sm z-10">
        <div className="flex gap-2">
          <Input
            placeholder={
              !activeModel
                ? "Selecciona un modelo para chatear..."
                : !activeConversation
                ? "Inicia o selecciona una conversación..."
                : activeServers.length === 0
                ? "Activa un servidor MCP para continuar..."
                : "Escribe tu mensaje..."
            }
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={!activeModel || !activeConversation || activeServers.length === 0 || isLoading}
            className="flex-1 hover-glow focus:ring-2 focus:ring-primary/30 transition-all duration-300"
          />
          <Button 
            size="icon" 
            variant="ghost"
            disabled={!activeModel || !activeConversation || activeServers.length === 0 || isLoading}
            className="hover-scale"
          >
            <PaperclipIcon size={18} />
          </Button>
          <Button
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || !activeModel || !activeConversation || activeServers.length === 0 || isLoading}
            className="hover-scale gradient-btn text-primary-foreground"
          >
            <SendIcon size={18} />
          </Button>
        </div>
      </div>
    </div>
  );
}
