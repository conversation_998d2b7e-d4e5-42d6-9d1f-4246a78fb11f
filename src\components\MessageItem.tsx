
import { ChatMessage } from "@/types/mcp";
import { cn } from "@/lib/utils";
import { UserAvatar, BotAvatar, SystemAvatar, ToolAvatar } from "@/components/Avatars";
import { Card, CardContent } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, Code, CopyIcon, CheckIcon, PlayIcon } from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface MessageItemProps {
  message: ChatMessage;
}

export function MessageItem({ message }: MessageItemProps) {
  const [isToolExpanded, setIsToolExpanded] = useState(false);
  const [copied, setCopied] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);
  const messageRef = useRef<HTMLDivElement>(null);
  
  // Animation effect when message appears
  useEffect(() => {
    setAnimationComplete(false);
    const timeout = setTimeout(() => setAnimationComplete(true), 500);
    return () => clearTimeout(timeout);
  }, [message.id]);
  
  const getAvatar = () => {
    switch (message.role) {
      case 'user':
        return <UserAvatar />;
      case 'assistant':
        return <BotAvatar />;
      case 'system':
        return <SystemAvatar />;
      case 'tool':
        return <ToolAvatar />;
      default:
        return <BotAvatar />;
    }
  };
  
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  // Helper to identify and format code blocks in text
  const formatMessageContent = (content: string) => {
    // Split by code block markers
    const parts = content.split(/```([^`]+)```/);
    
    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // This is a code block
        return (
          <div key={index} className="relative my-3 group">
            <Card className="bg-muted/70 overflow-hidden">
              <CardContent className="p-0">
                <div className="flex items-center justify-between bg-muted/90 p-2 text-xs font-mono text-muted-foreground">
                  <div className="flex items-center">
                    <Code size={14} className="mr-1" />
                    <span>Code</span>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6 opacity-70 hover:opacity-100"
                    onClick={() => copyToClipboard(part)}
                  >
                    {!copied ? <CopyIcon size={14} /> : <CheckIcon size={14} />}
                  </Button>
                </div>
                <pre className="p-4 text-sm overflow-x-auto bg-muted/30">
                  <code>{part}</code>
                </pre>
              </CardContent>
            </Card>
          </div>
        );
      } else if (part.trim()) {
        // This is normal text
        return (
          <div key={index} className="prose prose-sm dark:prose-invert max-w-none">
            {part.split('\n').map((line, i) => (
              <p key={i}>{line}</p>
            ))}
          </div>
        );
      }
      return null;
    });
  };
  
  return (
    <div 
      ref={messageRef}
      className={cn(
        "flex items-start gap-3 py-4 message-appear",
        message.role === 'user' ? "justify-start" : "justify-start",
        animationComplete ? "" : "opacity-0 translate-y-4"
      )}
    >
      {getAvatar()}
      
      <div className={cn(
        "flex-1 space-y-2 p-4 rounded-lg transition-all",
        message.role === 'assistant' ? "bg-primary/5" : "",
        message.role === 'user' ? "bg-accent/40" : "",
        message.role === 'system' ? "bg-muted/50" : "",
        message.role === 'tool' ? "bg-accent/20" : "",
        "hover-glow"
      )}>
        <div className="flex items-center gap-2">
          <span className={cn(
            "font-medium capitalize px-2 py-0.5 rounded text-xs",
            message.role === 'assistant' ? "bg-primary text-primary-foreground" : "",
            message.role === 'user' ? "bg-accent text-accent-foreground" : "",
            message.role === 'system' ? "bg-muted text-muted-foreground" : "",
            message.role === 'tool' ? "bg-accent/50 text-accent-foreground" : ""
          )}>
            {message.role}
          </span>
          <span className="text-xs text-muted-foreground">{formatDate(message.timestamp)}</span>
        </div>
        
        <div className="prose prose-sm dark:prose-invert max-w-none">
          {formatMessageContent(message.content)}
        </div>
        
        {message.toolCalls && message.toolCalls.length > 0 && (
          <Collapsible
            open={isToolExpanded}
            onOpenChange={setIsToolExpanded}
            className="mt-2"
          >
            <div className="flex items-center">
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1 hover-scale">
                  {isToolExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                  <span className="text-xs font-medium">
                    {message.toolCalls.length} Tool {message.toolCalls.length === 1 ? "Call" : "Calls"}
                  </span>
                </Button>
              </CollapsibleTrigger>
            </div>
            <CollapsibleContent className="mt-2 space-y-2">
              {message.toolCalls.map((toolCall, idx) => (
                <Card key={toolCall.id} className="overflow-hidden glass hover-scale transition-all duration-300">
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <PlayIcon size={16} className="text-primary" />
                        <div className="text-sm font-medium">{toolCall.toolId}</div>
                      </div>
                      <div className={cn(
                        "text-xs px-2 py-0.5 rounded-full",
                        toolCall.status === 'running' ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300" : "",
                        toolCall.status === 'completed' ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" : "",
                        toolCall.status === 'error' ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" : ""
                      )}>
                        {toolCall.status}
                      </div>
                    </div>
                    <div className="mt-2">
                      <div className="text-xs text-muted-foreground mb-1">Parameters:</div>
                      <pre className="text-xs bg-muted/50 p-2 rounded overflow-auto">
                        {JSON.stringify(toolCall.params, null, 2)}
                      </pre>
                    </div>
                    {message.toolResults?.find(r => r.toolCallId === toolCall.id) && (
                      <div className="mt-3 pt-2 border-t border-border">
                        <div className="text-xs font-medium mb-1">Result:</div>
                        <pre className="text-xs bg-muted/50 p-2 rounded overflow-auto">
                          {JSON.stringify(
                            message.toolResults.find(r => r.toolCallId === toolCall.id)?.result,
                            null,
                            2
                          )}
                        </pre>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </CollapsibleContent>
          </Collapsible>
        )}
      </div>
    </div>
  );
}
