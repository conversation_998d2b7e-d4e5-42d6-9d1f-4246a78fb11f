
import { useMCP } from "@/context/MCPContext";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Check, ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useState } from "react";

export function ModelSelect() {
  const { activeServers, activeModel, setActiveModel, providers, models, refreshServersAndModels } = useMCP();
  const [isOpen, setIsOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Agrupa modelos de proveedores API
  const modelsByProvider = providers.map(provider => {
    const providerModels = models.filter(model => model.provider === provider.id);
    return {
      sourceType: 'provider',
      sourceId: provider.id,
      sourceName: provider.name,
      models: providerModels,
    };
  }).filter(group => group.models.length > 0);

  // Agrupa modelos de servidores MCP activos
  const modelsByServer = activeServers.map(server => {
    const serverModels = models.filter(model => model.provider === server.id);
    return {
      sourceType: 'server',
      sourceId: server.id,
      sourceName: server.name,
      models: serverModels,
    };
  }).filter(group => group.models.length > 0);

  // Unifica todos los grupos
  const allModelGroups = [...modelsByProvider, ...modelsByServer];
  const hasModels = allModelGroups.some(group => group.models.length > 0);

  if (!hasModels) {
    return (
      <div className="flex gap-2">
        <Button variant="outline" disabled className="text-sm flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-amber-500" />
          <span>No models available</span>
        </Button>
        <Button variant="ghost" size="icon" onClick={async () => {
          setRefreshing(true);
          await refreshServersAndModels();
          setRefreshing(false);
        }} disabled={refreshing}>
          {refreshing ? <span className="animate-spin">⟳</span> : "⟳"}
        </Button>
      </div>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          {activeModel ? (
            <>
              <span className="truncate max-w-[120px]">{activeModel.name}</span>
              {activeModel.provider && (
                <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                  {providers.find(p => p.id === activeModel.provider)?.name || activeModel.provider}
                </span>
              )}
            </>
          ) : (
            "Select a model"
          )}
          <ChevronDown size={16} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[280px] p-1" align="end">
        <div className="flex justify-end p-1">
          <Button variant="ghost" size="icon" onClick={async () => {
            setRefreshing(true);
            await refreshServersAndModels();
            setRefreshing(false);
          }} disabled={refreshing} title="Refrescar modelos">
            {refreshing ? <span className="animate-spin">⟳</span> : "⟳"}
          </Button>
        </div>
        <ScrollArea className="h-[260px]">
          {refreshing ? (
            <div className="space-y-2 p-2">
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-5 w-3/4 mt-2" />
              <Skeleton className="h-8 w-full" />
            </div>
           ) : allModelGroups.length === 0 ? (
            <div className="p-2 text-center">
              <p className="text-sm text-muted-foreground">No models found</p>
              <p className="text-xs text-muted-foreground mt-1">Check provider/server configuration</p>
            </div>
          ) : (
            allModelGroups.map((group, groupIndex) => (
              <div key={group.sourceId}>
                {groupIndex > 0 && <Separator className="my-2" />}
                <div className="px-2 py-1.5 text-xs font-medium">
                  {group.sourceType === 'provider' ? (
                    <span className="text-primary">{group.sourceName}</span>
                  ) : (
                    <span className="text-blue-700">{group.sourceName} (MCP)</span>
                  )}
                </div>
                <div className="space-y-1">
                  {group.models.map((model) => (
                    <Button
                      key={model.id}
                      variant="ghost"
                      className="w-full justify-start flex items-center gap-2"
                      onClick={() => {
                        setActiveModel(model.id);
                        setIsOpen(false);
                      }}
                    >
                      <span className="truncate">{model.name}</span>
                      {model.contextLength && (
                        <span className="text-xs text-muted-foreground ml-1">
                          {Math.round(model.contextLength/1000)}K
                        </span>
                      )}
                      {activeModel?.id === model.id && (
                        <Check size={16} className="ml-auto" />
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            ))
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
