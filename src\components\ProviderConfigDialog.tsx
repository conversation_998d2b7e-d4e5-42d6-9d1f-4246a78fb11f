import { useState } from "react";
import { useMCP } from "@/context/MCPContext";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ProviderConfigDialogProps {
  isOpen: boolean;
  onClose: () => void;
  providerId?: string | null;
}

export function ProviderConfigDialog({ isOpen, onClose, providerId }: ProviderConfigDialogProps) {
  const { providers, updateProviderConfig } = useMCP();
  const provider = providers.find(p => p.id === providerId);
  const [apiKey, setApiKey] = useState(provider?.apiKey || "");
  const [endpoint, setEndpoint] = useState(provider?.endpoint || "");

  if (!provider) return null;

  const handleSave = () => {
    updateProviderConfig(provider.id, { apiKey, endpoint });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Configure {provider.name}</DialogTitle>
          <DialogDescription>
            Set API Key and endpoint for this provider.
          </DialogDescription>
        </DialogHeader>
        {provider.requiresApiKey && (
          <div className="mb-4">
            <Label htmlFor="apiKey">API Key</Label>
            <Input id="apiKey" type="text" value={apiKey} onChange={e => setApiKey(e.target.value)} autoFocus />
          </div>
        )}
        {provider.requiresEndpoint && (
          <div className="mb-4">
            <Label htmlFor="endpoint">Endpoint</Label>
            <Input id="endpoint" type="text" value={endpoint} onChange={e => setEndpoint(e.target.value)} />
          </div>
        )}
        <DialogFooter>
          <Button onClick={onClose} variant="outline">Cancel</Button>
          <Button onClick={handleSave} className="gradient-btn">Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
