import { useState } from "react";
import { useMCP } from "@/context/MCPContext";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ProviderConfigDialog } from "@/components/ProviderConfigDialog";
import { CheckCircle, AlertTriangle, Pencil } from "lucide-react";

export function ProvidersTab() {
  const { providers } = useMCP();
  const [editingProviderId, setEditingProviderId] = useState<string|null>(null);

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium">API Providers</h3>
        <div className="text-sm text-muted-foreground mb-2">Configure API keys and endpoints for each provider.</div>
      </div>
      <div className="grid grid-cols-1 gap-3">
        {providers.map(provider => {
          const isConfigured = provider.requiresApiKey
            ? !!provider.apiKey
            : provider.requiresEndpoint
              ? !!provider.endpoint
              : true;
          return (
            <Card key={provider.id} className="flex flex-row items-center justify-between p-3">
              <div className="flex-1">
                <CardHeader className="p-0 mb-1">
                  <CardTitle className="text-base font-semibold flex items-center gap-2">
                    {provider.name}
                    {isConfigured ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </CardTitle>
                  <CardDescription className="text-xs text-muted-foreground">
                    {provider.type.charAt(0).toUpperCase() + provider.type.slice(1)}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="text-xs">
                    {provider.requiresApiKey && (
                      <span>API Key: {provider.apiKey ? "Configured" : <span className="text-yellow-600">Not set</span>}</span>
                    )}
                    {provider.requiresEndpoint && (
                      <span className="ml-2">Endpoint: {provider.endpoint || <span className="text-yellow-600">Not set</span>}</span>
                    )}
                  </div>
                </CardContent>
              </div>
              <Button variant="outline" size="sm" className="ml-4" onClick={() => setEditingProviderId(provider.id)}>
                <Pencil className="h-4 w-4 mr-1" /> Edit
              </Button>
              <ProviderConfigDialog isOpen={editingProviderId === provider.id} onClose={() => setEditingProviderId(null)} providerId={provider.id} />
            </Card>
          );
        })}
      </div>
    </div>
  );
}
