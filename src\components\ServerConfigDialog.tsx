
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useMCP } from "@/context/MCPContext";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "sonner";
import { MCPServer } from "@/types/mcp";
import { Textarea } from "./ui/textarea";

interface ServerConfigDialogProps {
  isOpen: boolean;
  onClose: () => void;
  serverToEdit?: MCPServer;
}

export function ServerConfigDialog({ isOpen, onClose, serverToEdit }: ServerConfigDialogProps) {
  const { addServer, updateServer } = useMCP();
  const navigate = useNavigate();
  
  const [serverName, setServerName] = useState("");
  const [connectionType, setConnectionType] = useState<"sse" | "stdio">("sse");
  const [serverUrl, setServerUrl] = useState("");
  const [executablePath, setExecutablePath] = useState("");
  const [commandArgs, setCommandArgs] = useState("");
  const [envVars, setEnvVars] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  const [serverId, setServerId] = useState<string | undefined>(undefined);
  
  // Populate form when editing an existing server
  useEffect(() => {
    if (serverToEdit) {
      setIsEditMode(true);
      setServerId(serverToEdit.id);
      setServerName(serverToEdit.name);
      setConnectionType(serverToEdit.connectionType);
      setServerUrl(serverToEdit.url || "");
      setExecutablePath(serverToEdit.path || "");
      
      if (serverToEdit.stdioConfig) {
        // Set command args as space-separated string
        setCommandArgs(serverToEdit.stdioConfig.args.join(" "));
        
        // Set environment variables as KEY=VALUE newline-separated string
        const envString = Object.entries(serverToEdit.stdioConfig.env)
          .map(([key, value]) => `${key}=${value}`)
          .join("\n");
        setEnvVars(envString);
      }
    } else {
      // Reset form for new server
      setIsEditMode(false);
      setServerId(undefined);
      setServerName("");
      setConnectionType("sse");
      setServerUrl("");
      setExecutablePath("");
      setCommandArgs("");
      setEnvVars("");
    }
  }, [serverToEdit, isOpen]);

  const parseEnvVars = (envText: string): Record<string, string> => {
    const result: Record<string, string> = {};
    if (!envText.trim()) return result;
    
    envText.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;
      
      const firstEqualPos = trimmedLine.indexOf('=');
      if (firstEqualPos > 0) {
        const key = trimmedLine.substring(0, firstEqualPos).trim();
        const value = trimmedLine.substring(firstEqualPos + 1).trim();
        if (key) {
          result[key] = value;
        }
      }
    });
    
    return result;
  };
  
  const handleAddOrUpdateServer = () => {
    if (!serverName.trim()) {
      toast.error("Server name is required");
      return;
    }
    
    if (connectionType === "sse" && !serverUrl.trim()) {
      toast.error("Server URL is required");
      return;
    }
    
    if (connectionType === "stdio" && !executablePath.trim()) {
      toast.error("Executable path is required");
      return;
    }
    
    // Parse environment variables
    const envVarsObj = parseEnvVars(envVars);
    
    const serverData: MCPServer = {
      id: isEditMode && serverId ? serverId : crypto.randomUUID(),
      name: serverName.trim(),
      connectionType,
      url: connectionType === "sse" ? serverUrl.trim() : undefined,
      path: connectionType === "stdio" ? executablePath.trim() : undefined,
      isActive: true,
      stdioConfig: connectionType === "stdio" ? {
        command: executablePath.trim(),
        args: commandArgs.trim() ? commandArgs.trim().split(/\s+/) : [],
        env: envVarsObj
      } : undefined
    };
    
    try {
      if (isEditMode) {
        updateServer(serverData);
        toast.success(`Server "${serverName}" updated successfully`);
      } else {
        addServer(serverData);
        toast.success(`Server "${serverName}" added successfully`);
      }
      
      // Reset form
      setServerName("");
      setServerUrl("");
      setExecutablePath("");
      setCommandArgs("");
      setEnvVars("");
      
      onClose();
      
      // Navigate to the server details page
      navigate(`/server/${serverData.id}`);
    } catch (error) {
      console.error("Error adding/updating server:", error);
      toast.error(`Failed to ${isEditMode ? 'update' : 'add'} server: ${error}`);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] glass">
        <DialogHeader>
          <DialogTitle className="text-primary">
            {isEditMode ? "Edit Server" : "Add Server"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? "Update your MCP server connection settings."
              : "Configure a connection to an MCP server."
            }
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={serverName}
              onChange={(e) => setServerName(e.target.value)}
              className="col-span-3"
              placeholder="My MCP Server"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">Type</Label>
            <RadioGroup
              value={connectionType}
              onValueChange={(value) => setConnectionType(value as "sse" | "stdio")}
              className="col-span-3 flex flex-col space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="sse" id="sse" />
                <Label htmlFor="sse" className="cursor-pointer">Server Sent Events (SSE)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="stdio" id="stdio" />
                <Label htmlFor="stdio" className="cursor-pointer">Standard I/O (STDIO)</Label>
              </div>
            </RadioGroup>
          </div>
          
          {connectionType === "sse" ? (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="url" className="text-right">
                URL
              </Label>
              <Input
                id="url"
                value={serverUrl}
                onChange={(e) => setServerUrl(e.target.value)}
                className="col-span-3"
                placeholder="https://your-mcp-server.com/v1"
              />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="path" className="text-right">
                  Path
                </Label>
                <Input
                  id="path"
                  value={executablePath}
                  onChange={(e) => setExecutablePath(e.target.value)}
                  className="col-span-3"
                  placeholder="/path/to/executable or command"
                />
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="args" className="text-right">
                  Arguments
                </Label>
                <Input
                  id="args"
                  value={commandArgs}
                  onChange={(e) => setCommandArgs(e.target.value)}
                  className="col-span-3"
                  placeholder="--config /path/to/config.json"
                />
                <div className="col-start-2 col-span-3 text-xs text-muted-foreground">
                  Space-separated command arguments
                </div>
              </div>
              
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="env" className="text-right mt-2">
                  Environment
                </Label>
                <div className="col-span-3 space-y-1">
                  <Textarea
                    id="env"
                    value={envVars}
                    onChange={(e) => setEnvVars(e.target.value)}
                    className="min-h-[100px] font-mono text-sm"
                    placeholder="KEY=value
ANOTHER_KEY=another_value"
                  />
                  <div className="text-xs text-muted-foreground">
                    One environment variable per line in KEY=value format
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <Button 
            onClick={handleAddOrUpdateServer} 
            className="hover-scale gradient-btn text-primary-foreground"
          >
            {isEditMode ? "Update Server" : "Add Server"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
