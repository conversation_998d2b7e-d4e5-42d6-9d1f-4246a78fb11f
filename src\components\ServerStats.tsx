import { useMCP } from "@/context/MCPContext";
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle, 
  InfoIcon, 
  ServerIcon, 
  Wrench 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { useState } from "react";

export function ServerStats() {
  const { servers, tools, models, activeServers, fetchModels, fetchTools } = useMCP();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const activeServerCount = activeServers.length;
  const totalServerCount = servers.length;
  const totalToolCount = tools.length;
  const totalModelCount = models.length;
  
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      toast.info("Refreshing server data...");
      
      // This will now actually refresh the models and tools from all active servers
      const refreshPromises = activeServers.map(async (server) => {
        try {
          // In parallel fetch models and tools for each active server
          return Promise.all([
            fetchModels(server.id),
            fetchTools(server.id)
          ]);
        } catch (error) {
          console.error(`Failed to refresh data for server ${server.name}:`, error);
          return Promise.resolve();
        }
      });
      
      await Promise.all(refreshPromises);
      toast.success("Server data refreshed");
    } catch (error) {
      console.error("Failed to refresh server data:", error);
      toast.error("Failed to refresh server data");
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Get the status of the servers
  const getServerStatus = () => {
    if (totalServerCount === 0) {
      return { status: "error" as const, message: "No servers configured" };
    }
    if (activeServerCount === 0) {
      return { status: "warning" as const, message: "No active servers" };
    }
    return { status: "success" as const, message: "Servers active" };
  };
  
  // Get the status of the tools
  const getToolsStatus = () => {
    if (activeServerCount === 0) {
      return { status: "disabled" as const, message: "No active servers" };
    }
    if (totalToolCount === 0) {
      return { status: "warning" as const, message: "No tools found" };
    }
    return { status: "success" as const, message: `${totalToolCount} tools available` };
  };
  
  // Get the status of the models
  const getModelsStatus = () => {
    if (activeServerCount === 0) {
      return { status: "disabled" as const, message: "No active servers" };
    }
    if (totalModelCount === 0) {
      return { status: "warning" as const, message: "No models found" };
    }
    return { status: "success" as const, message: `${totalModelCount} models available` };
  };
  
  const serverStatus = getServerStatus();
  const toolsStatus = getToolsStatus();
  const modelsStatus = getModelsStatus();
  
  return (
    <div className="flex flex-col space-y-2 bg-background/80 backdrop-blur-sm p-3 rounded-lg border shadow-sm">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Server Status</h3>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 px-2" 
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          {isRefreshing ? "Refreshing..." : "Refresh"}
        </Button>
      </div>
      
      <div className="flex flex-col space-y-1">
        <StatusItem 
          label="Servers"
          value={`${activeServerCount}/${totalServerCount} active`}
          status={serverStatus.status}
          icon={<ServerIcon className="h-4 w-4" />}
          details={serverStatus.message}
          serversDetails={servers.map(s => ({
            name: s.name,
            isActive: s.isActive,
            type: s.connectionType
          }))}
        />
        
        <StatusItem 
          label="Tools"
          value={totalToolCount.toString()}
          status={toolsStatus.status}
          icon={<Wrench className="h-4 w-4" />}
          details={toolsStatus.message}
          toolsDetails={tools.map(t => ({
            name: t.name,
            server: servers.find(s => s.id === t.serverId)?.name || 'Unknown'
          }))}
        />
        
        <StatusItem 
          label="Models"
          value={totalModelCount.toString()}
          status={modelsStatus.status}
          icon={<InfoIcon className="h-4 w-4" />}
          details={modelsStatus.message}
          modelsDetails={models.map(m => ({
            name: m.name,
            provider: m.provider
          }))}
        />
      </div>
    </div>
  );
}

type StatusItemProps = {
  label: string;
  value: string;
  status: "success" | "warning" | "error" | "disabled";
  icon: React.ReactNode;
  details: string;
  serversDetails?: {name: string, isActive: boolean, type: string}[];
  toolsDetails?: {name: string, server: string}[];
  modelsDetails?: {name: string, provider: string}[];
};

function StatusItem({ 
  label, 
  value, 
  status, 
  icon, 
  details,
  serversDetails,
  toolsDetails,
  modelsDetails
}: StatusItemProps) {
  const getStatusColor = () => {
    switch (status) {
      case "success":
        return "text-green-500";
      case "warning":
        return "text-yellow-500";
      case "error":
        return "text-red-500";
      case "disabled":
        return "text-gray-400";
      default:
        return "text-gray-500";
    }
  };
  
  const getStatusIcon = () => {
    switch (status) {
      case "success":
        return <CheckCircle className={`h-4 w-4 ${getStatusColor()}`} />;
      case "warning":
      case "error":
        return <AlertTriangle className={`h-4 w-4 ${getStatusColor()}`} />;
      default:
        return null;
    }
  };
  
  const renderDetails = () => {
    if (serversDetails) {
      return (
        <div className="space-y-2">
          <p className="text-sm font-medium">Server Details</p>
          {serversDetails.length === 0 ? (
            <p className="text-xs text-muted-foreground">No servers configured</p>
          ) : (
            <div className="space-y-1">
              {serversDetails.map((server, i) => (
                <div key={i} className="flex items-center justify-between">
                  <span className="text-xs">{server.name} ({server.type})</span>
                  <span className={`text-xs ${server.isActive ? 'text-green-500' : 'text-gray-400'}`}>
                    {server.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    if (toolsDetails) {
      return (
        <div className="space-y-2">
          <p className="text-sm font-medium">Tools Available</p>
          {toolsDetails.length === 0 ? (
            <p className="text-xs text-muted-foreground">No tools found</p>
          ) : (
            <div className="space-y-1">
              {toolsDetails.map((tool, i) => (
                <div key={i} className="flex items-center justify-between">
                  <span className="text-xs">{tool.name}</span>
                  <span className="text-xs text-muted-foreground">{tool.server}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    if (modelsDetails) {
      return (
        <div className="space-y-2">
          <p className="text-sm font-medium">Models Available</p>
          {modelsDetails.length === 0 ? (
            <p className="text-xs text-muted-foreground">No models found</p>
          ) : (
            <div className="space-y-1">
              {modelsDetails.map((model, i) => (
                <div key={i} className="flex items-center justify-between">
                  <span className="text-xs">{model.name}</span>
                  <span className="text-xs text-muted-foreground">{model.provider}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    return <p className="text-sm">{details}</p>;
  };
  
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <div className={`flex items-center justify-between px-2 py-1 rounded hover:bg-accent/50 cursor-default ${status === 'disabled' ? 'opacity-50' : ''}`}>
          <div className="flex items-center space-x-2">
            <span className={getStatusColor()}>{icon}</span>
            <span className="text-sm">{label}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">{value}</span>
            {getStatusIcon()}
          </div>
        </div>
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <div className="space-y-2">
          <h4 className="font-medium">{label} Status</h4>
          <Separator />
          {renderDetails()}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
