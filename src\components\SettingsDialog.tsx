
import { useState } from "react";
import { useMCP } from "@/context/MCPContext";
import { useTheme } from "@/components/ThemeProvider";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Moon, Sun, Monitor } from "lucide-react";
import { ProviderConfigDialog } from "@/components/ProviderConfigDialog";
import { ProvidersTab } from "@/components/ProvidersTab";

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SettingsDialog({ isOpen, onClose }: SettingsDialogProps) {
  const { servers, tools, models } = useMCP();
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState("general");
  const [autoSave, setAutoSave] = useState(true);
  const [streaming, setStreaming] = useState(true);
  const [maxContextLength, setMaxContextLength] = useState(8192);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] glass">
        <DialogHeader>
          <DialogTitle className="text-primary">Settings</DialogTitle>
          <DialogDescription>
            Configure your MCP Client settings. Changes are saved automatically.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="connection">Connection</TabsTrigger>
            <TabsTrigger value="providers">Providers</TabsTrigger>
            <TabsTrigger value="about">About</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 slide-in-up">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="streaming">Message Streaming</Label>
                <div className="text-sm text-muted-foreground">Enable streaming for responses</div>
              </div>
              <Switch id="streaming" checked={streaming} onCheckedChange={setStreaming} />
            </div>
            
            <Separator />
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="autosave">Auto-save Conversations</Label>
                <div className="text-sm text-muted-foreground">Automatically save chat history</div>
              </div>
              <Switch id="autosave" checked={autoSave} onCheckedChange={setAutoSave} />
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label htmlFor="context-length">Max Context Length</Label>
              <div className="flex items-center gap-2">
                <Input 
                  id="context-length" 
                  type="number" 
                  value={maxContextLength} 
                  onChange={(e) => setMaxContextLength(parseInt(e.target.value))} 
                  className="w-32"
                />
                <span className="text-sm text-muted-foreground">tokens</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Maximum context length for conversations. Higher values use more memory.
              </div>
            </div>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-4 slide-in-up">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Theme</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className={`flex-1 ${theme === 'light' ? 'border-primary' : ''}`}
                    onClick={() => setTheme("light")}
                  >
                    <Sun className="h-5 w-5 mr-2" />
                    Light
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className={`flex-1 ${theme === 'dark' ? 'border-primary' : ''}`}
                    onClick={() => setTheme("dark")}
                  >
                    <Moon className="h-5 w-5 mr-2" />
                    Dark
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className={`flex-1 ${theme === 'system' ? 'border-primary' : ''}`}
                    onClick={() => setTheme("system")}
                  >
                    <Monitor className="h-5 w-5 mr-2" />
                    System
                  </Button>
                </div>
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label>Accent Color</Label>
              <div className="flex gap-2">
                {["#005BA7", "#8B5CF6", "#F97316", "#0EA5E9", "#10B981"].map((color) => (
                  <Button 
                    key={color}
                    variant="outline"
                    className="w-10 h-10 rounded-full p-0 border-2 hover-scale"
                    style={{ backgroundColor: color, borderColor: color === "#005BA7" ? "white" : "transparent" }}
                  />
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="connection" className="space-y-4 slide-in-up">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Servers ({servers.length})</h3>
                <div className="text-sm text-muted-foreground">Manage your MCP servers</div>
              </div>
              <ScrollArea className="h-36 border rounded-md p-2">
                {servers.map(server => (
                  <div key={server.id} className="flex items-center justify-between py-2">
                    <div className="text-sm">
                      <div className="font-medium">{server.name}</div>
                      <div className="text-xs text-muted-foreground">{server.connectionType}: {server.url || server.path}</div>
                    </div>
                    <div className={`w-2 h-2 rounded-full ${server.isActive ? "bg-green-500" : "bg-gray-300"}`}></div>
                  </div>
                ))}
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="providers" className="space-y-4 slide-in-up">
            <ProvidersTab />
          </TabsContent>
          
          <TabsContent value="about" className="space-y-4 slide-in-up">
            <div className="flex flex-col items-center justify-center py-4">
              <img 
                src="/logo.png" 
                alt="Grupo SAI Logo" 
                className="h-16 mb-4 float"
              />
              <h2 className="text-xl font-bold mb-2 text-primary">MCP Client</h2>
              <p className="text-sm text-muted-foreground text-center max-w-sm">
                A powerful client for interacting with Multiple Choice Protocol (MCP) servers, 
                enabling seamless communication with various LLM models and tools.
              </p>
              <div className="mt-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Version:</span>
                  <span>1.0.0</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Created by:</span>
                  <span>Grupo SAI</span>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button onClick={onClose} className="hover-scale gradient-btn text-primary-foreground">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
