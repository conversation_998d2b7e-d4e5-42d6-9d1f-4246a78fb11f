
import { useState } from "react";
import { useMCP } from "@/context/MCPContext";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ServerConfigDialog } from "@/components/ServerConfigDialog";
import { SettingsDialog } from "@/components/SettingsDialog";
import {
  PlusIcon,
  ServerIcon,
  MessageSquareIcon,
  SettingsIcon,
  BrainIcon,
  CheckSquareIcon,
  Edit,
  Trash
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

export function Sidebar() {
  const {
    servers,
    models,
    conversations,
    activeServerIds,
    activeModel,
    activeConversation,
    activeServers,
    providers,
    toggleServerActive,
    removeServer,
    setActiveModel,
    setActiveConversation,
    createConversation
  } = useMCP();

  const [isServerDialogOpen, setIsServerDialogOpen] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [sidebarTab, setSidebarTab] = useState<'servers' | 'models' | 'chats'>('servers');
  const [serverToEdit, setServerToEdit] = useState<string | null>(null);
  const [serverToDelete, setServerToDelete] = useState<string | null>(null);

  const handleNewChat = () => {
    if (activeServers.length > 0 && activeModel) {
      createConversation(activeModel.id);
    }
  };

  const handleServerToggle = (serverId: string, isActive: boolean) => {
    toggleServerActive(serverId, !isActive);
  };

  const handleEditServer = (serverId: string) => {
    setServerToEdit(serverId);
    setIsServerDialogOpen(true);
  };

  const handleDeleteServer = (serverId: string) => {
    setServerToDelete(serverId);
  };

  const confirmDeleteServer = () => {
    if (serverToDelete) {
      try {
        // Find the server name for the toast message
        const serverName = servers.find(s => s.id === serverToDelete)?.name || "Server";
        removeServer(serverToDelete);
        toast.success(`Server "${serverName}" deleted successfully`);
      } catch (error) {
        console.error("Error deleting server:", error);
        toast.error(`Failed to delete server: ${error}`);
      }
      setServerToDelete(null);
    }
  };

  return (
    <div className="w-64 h-screen flex flex-col border-r border-border bg-sidebar relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute -bottom-16 -right-16 w-40 h-40 bg-sidebar-primary/5 rounded-full blur-xl"></div>
      <div className="absolute -top-8 -left-8 w-32 h-32 bg-sidebar-primary/10 rounded-full blur-lg"></div>
      
      <div className="flex items-center justify-between p-4 relative">
        <div className="flex items-center gap-2">
          <img 
            src="/logo.png" 
            alt="Grupo SAI Logo" 
            className="h-8 float"
          />
          <h1 className="text-lg font-bold text-sidebar-primary">MCP Client</h1>
        </div>
        <Button
          size="icon"
          variant="outline"
          onClick={() => {
            setServerToEdit(null); // Ensure we're adding a new server
            setIsServerDialogOpen(true);
          }}
          className="h-8 w-8 bg-sidebar-accent text-sidebar-primary hover-scale"
        >
          <PlusIcon size={16} />
        </Button>
      </div>

      <div className="flex border-b border-sidebar-border">
        <Button
          variant={sidebarTab === 'servers' ? 'secondary' : 'ghost'}
          className="flex-1 rounded-none py-2 px-0 transition-all duration-200"
          onClick={() => setSidebarTab('servers')}
        >
          <ServerIcon size={16} className="mr-1" />
          Servers
        </Button>
        <Button
          variant={sidebarTab === 'models' ? 'secondary' : 'ghost'}
          className="flex-1 rounded-none py-2 px-0 transition-all duration-200"
          onClick={() => setSidebarTab('models')}
        >
          <BrainIcon size={16} className="mr-1" />
          Models
        </Button>
        <Button
          variant={sidebarTab === 'chats' ? 'secondary' : 'ghost'}
          className="flex-1 rounded-none py-2 px-0 transition-all duration-200"
          onClick={() => setSidebarTab('chats')}
        >
          <MessageSquareIcon size={16} className="mr-1" />
          Chats
        </Button>
      </div>

      <ScrollArea className="flex-1 p-2">
        <div className={`transition-opacity duration-300 ${sidebarTab === 'servers' ? 'opacity-100' : 'opacity-0 hidden'}`}>
          <div className="space-y-1">
            {servers.length === 0 ? (
              <div className="text-center p-4 text-sidebar-foreground/70 slide-in-up">
                <p>No servers configured</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2 hover-scale hover-glow"
                  onClick={() => {
                    setServerToEdit(null);
                    setIsServerDialogOpen(true);
                  }}
                >
                  Add Server
                </Button>
              </div>
            ) : (
              servers.map((server, index) => (
                <div 
                  key={server.id} 
                  className={`flex items-center justify-between mb-1 p-1 rounded-md ${
                    activeServerIds?.includes(server.id) ? "bg-secondary/50" : ""
                  } hover:bg-secondary/30 transition-colors`}
                >
                  <Button
                    variant={activeServerIds?.includes(server.id) ? "secondary" : "ghost"}
                    className={`justify-start text-left hover-scale ${index % 2 === 0 ? 'slide-in-right' : 'slide-in-up'}`}
                    style={{ animationDelay: `${index * 0.05}s` }}
                  >
                    <ServerIcon size={16} className="mr-2" />
                    {server.name}
                  </Button>
                  <div className="flex">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`px-1 ${server.isActive ? 'text-green-500' : 'text-muted-foreground'}`}
                      onClick={() => handleServerToggle(server.id, server.isActive)}
                      title={server.isActive ? "Deactivate server" : "Activate server"}
                    >
                      <CheckSquareIcon size={16} className={server.isActive ? 'opacity-100' : 'opacity-30'} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="px-1 text-muted-foreground"
                      onClick={() => handleEditServer(server.id)}
                      title="Edit server"
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="px-1 text-destructive"
                      onClick={() => handleDeleteServer(server.id)}
                      title="Delete server"
                    >
                      <Trash size={16} />
                    </Button>
                  </div>
                </div>
              ))
            )}
            
            <div className="text-center mt-4">
              <Button 
                variant="outline" 
                size="sm" 
                className="hover-scale hover-glow"
                onClick={() => {
                  setServerToEdit(null);
                  setIsServerDialogOpen(true);
                }}
              >
                <PlusIcon size={14} className="mr-1" />
                Add Server
              </Button>
            </div>
          </div>
        </div>

        <div className={`transition-opacity duration-300 ${sidebarTab === 'models' ? 'opacity-100' : 'opacity-0 hidden'}`}>
          <div className="space-y-1">
            {activeServers.length === 0 ? (
              <div className="text-center p-4 text-sidebar-foreground/70">
                <p>No active servers</p>
                <p className="text-xs mt-1">Activate a server first</p>
              </div>
            ) : models.length === 0 ? (
              <div className="text-center p-4 text-sidebar-foreground/70">
                <p>No models available</p>
              </div>
            ) : (
              <>
                {providers.map((provider) => {
                  const providerModels = models.filter(model => model.provider === provider.id);
                  
                  if (providerModels.length === 0) return null;
                  
                  return (
                    <div key={provider.id} className="mb-3">
                      <div className="text-xs font-medium text-muted-foreground mb-1 pl-1">
                        {provider.name}
                      </div>
                      {providerModels.map((model, index) => (
                        <Button
                          key={model.id}
                          variant={activeModel?.id === model.id ? "secondary" : "ghost"}
                          className={`w-full justify-start hover-scale ${index % 2 === 0 ? 'slide-in-right' : 'slide-in-up'}`}
                          style={{ animationDelay: `${index * 0.05}s` }}
                          onClick={() => setActiveModel(model.id)}
                          disabled={!model.isAvailable}
                        >
                          <BrainIcon size={16} className="mr-2" />
                          {model.name}
                          {model.contextLength && (
                            <span className="ml-auto text-xs opacity-50">
                              {Math.round(model.contextLength/1000)}K
                            </span>
                          )}
                        </Button>
                      ))}
                    </div>
                  );
                })}
              </>
            )}
          </div>
        </div>

        <div className={`transition-opacity duration-300 ${sidebarTab === 'chats' ? 'opacity-100' : 'opacity-0 hidden'}`}>
          <div className="space-y-1">
            {conversations.length === 0 ? (
              <div className="text-center p-4 text-sidebar-foreground/70 slide-in-up">
                <p>No conversations yet</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2 hover-scale hover-glow"
                  onClick={handleNewChat}
                  disabled={!activeModel}
                >
                  New Chat
                </Button>
              </div>
            ) : (
              <>
                <Button
                  variant="outline"
                  className="w-full justify-start mb-2 gradient-btn text-primary-foreground slide-in-up"
                  onClick={handleNewChat}
                  disabled={!activeModel}
                >
                  <PlusIcon size={16} className="mr-2" />
                  New Chat
                </Button>
                <Separator className="my-2" />
                {conversations.map((conversation, index) => (
                  <Button
                    key={conversation.id}
                    variant={activeConversation?.id === conversation.id ? "secondary" : "ghost"}
                    className={`w-full justify-start text-left truncate hover-scale ${index % 2 === 0 ? 'slide-in-right' : 'slide-in-up'}`}
                    style={{ animationDelay: `${index * 0.05}s` }}
                    onClick={() => setActiveConversation(conversation.id)}
                  >
                    <MessageSquareIcon size={16} className="mr-2 shrink-0" />
                    <span className="truncate">{conversation.title}</span>
                  </Button>
                ))}
              </>
            )}
          </div>
        </div>
      </ScrollArea>

      <div className="p-3 border-t border-sidebar-border">
        <Button 
          variant="ghost" 
          className="w-full justify-start hover-scale"
          onClick={() => setIsSettingsDialogOpen(true)}
        >
          <SettingsIcon size={16} className="mr-2" />
          Settings
        </Button>
      </div>

      <ServerConfigDialog
        isOpen={isServerDialogOpen}
        onClose={() => {
          setIsServerDialogOpen(false);
          setServerToEdit(null);
        }}
        serverToEdit={serverToEdit ? servers.find(s => s.id === serverToEdit) : undefined}
      />
      
      <SettingsDialog
        isOpen={isSettingsDialogOpen}
        onClose={() => setIsSettingsDialogOpen(false)}
      />

      {/* Confirm Delete Dialog */}
      <AlertDialog open={!!serverToDelete} onOpenChange={(open) => !open && setServerToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Server</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this server? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteServer}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
