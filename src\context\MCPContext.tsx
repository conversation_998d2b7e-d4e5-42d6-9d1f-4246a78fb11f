import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { 
  MCPServer, 
  LLMModel, 
  MCPTool, 
  Conversation, 
  ChatMessage,
  ServerConnection,
  LLMProvider
} from "@/types/mcp";
import { toast } from "sonner";
import { useSecureStorage } from "@/hooks/use-secure-storage";
import { connectSSE, connectSTDIO, fetchModelsFromServer, fetchToolsFromServer } from "@/lib/server-connection";
import { 
  loadServersFromSupabase, 
  saveServerToSupabase, 
  deleteServerFromSupabase,
  updateServerActiveStatus
} from "@/utils/supabaseServerUtils";

// Default LLM providers with their characteristics
const DEFAULT_PROVIDERS: LLMProvider[] = [
  {
    id: "openai",
    name: "OpenA<PERSON>",
    type: "openai",
    requiresApiKey: true,
    requiresEndpoint: false,
    models: ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"]
  },
  {
    id: "anthropic",
    name: "Anthropic",
    type: "anthropic",
    requiresApiKey: true,
    requiresEndpoint: false,
    models: ["claude-3-5-sonnet", "claude-3-opus", "claude-3-haiku"]
  },
  {
    id: "gemini",
    name: "Google Gemini",
    type: "gemini",
    requiresApiKey: true,
    requiresEndpoint: false,
    models: ["gemini-1.5-pro", "gemini-1.5-flash"]
  },
  {
    id: "mistral",
    name: "Mistral AI",
    type: "mistral",
    requiresApiKey: true,
    requiresEndpoint: false,
    models: ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest"]
  },
  {
    id: "cohere",
    name: "Cohere",
    type: "cohere",
    requiresApiKey: true,
    requiresEndpoint: false,
    models: ["command-r-plus", "command-r"]
  },
  {
    id: "ollama",
    name: "Ollama",
    type: "ollama",
    requiresApiKey: false,
    requiresEndpoint: true,
    endpoint: "http://localhost:11434",
    models: ["llama3:70b", "llama3:8b"]
  }
];

interface MCPContextType {
  servers: MCPServer[];
  providers: LLMProvider[];
  models: LLMModel[];
  tools: MCPTool[];
  conversations: Conversation[];
  activeConversation: Conversation | null;
  activeModel: LLMModel | null;
  refreshServersAndModels: () => Promise<void>;

  activeServers: MCPServer[];
  activeServerIds: string[];
  connections: ServerConnection[];
  addServer: (server: MCPServer) => void;
  removeServer: (id: string) => void;
  updateServer: (server: MCPServer) => void;
  toggleServerActive: (id: string, isActive: boolean) => void;
  setActiveModel: (id: string | null) => void;
  setActiveConversation: (id: string | null) => void;
  createConversation: (modelId: string, title?: string) => Conversation;
  sendMessage: (content: string) => Promise<void>;
  fetchModels: (serverId: string, serverOverride?: MCPServer) => Promise<void>;
  fetchProviderModels: (providerId: string) => Promise<void>;
  fetchTools: (serverId: string, serverOverride?: MCPServer) => Promise<void>;
  connectToServer: (serverId: string) => Promise<boolean>;
  disconnectFromServer: (serverId: string) => void;
  updateProviderConfig: (providerId: string, config: Partial<LLMProvider>) => void;
  getProviderById: (id: string) => LLMProvider | undefined;
}

const MCPContext = createContext<MCPContextType | undefined>(undefined);

export const MCPProvider = ({ children }: { children: ReactNode }) => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [providers, setProviders] = useSecureStorage<LLMProvider[]>("mcp-providers", DEFAULT_PROVIDERS);
  const [models, setModels] = useState<LLMModel[]>([]);
  const [tools, setTools] = useState<MCPTool[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversationState] = useState<Conversation | null>(null);
  const [activeModel, setActiveModelState] = useState<LLMModel | null>(null);
  const [connections, setConnections] = useState<ServerConnection[]>([]);

  // Get active servers
  const activeServers = servers.filter(server => server.isActive);
  const activeServerIds = activeServers.map(server => server.id);

  // Mejor feedback visual si no hay modelos disponibles
  useEffect(() => {
    if (providers.length === 0 && servers.length === 0) {
      toast("Configura al menos un proveedor o servidor MCP para comenzar.", { duration: 4000 });
    } else if (models.length === 0) {
      toast("No hay modelos disponibles. Revisa la configuración de proveedores o servidores.", { duration: 4000 });
    }
  }, [providers.length, servers.length, models.length]);

  // Al iniciar, cargar servidores, proveedores y modelos
  useEffect(() => {
    const fetchAllModels = async () => {
      try {
        // Cargar servidores MCP desde Supabase
        const supabaseServers = await loadServersFromSupabase();
        setServers(supabaseServers);
        // Cargar modelos de proveedores API
        for (const provider of providers) {
          await fetchProviderModels(provider.id);
        }
        // Conectar y refrescar modelos/herramientas de los servidores activos
        for (const server of supabaseServers) {
          if (server.isActive) {
            await connectToServer(server.id);
            await fetchModels(server.id);
            await fetchTools(server.id);
          }
        }
      } catch (error) {
        console.error("Error cargando modelos/proveedores/servidores:", error);
        toast.error("Error al cargar modelos/proveedores/servidores");
      }
    };
    fetchAllModels();
    // Solo ejecutar en montaje inicial
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Guardar estado relevante en localStorage cuando cambie (para persistencia)
  useEffect(() => {
    localStorage.setItem('mcp-servers', JSON.stringify(servers));
    localStorage.setItem('mcp-conversations', JSON.stringify(conversations));
    localStorage.setItem('mcp-models', JSON.stringify(models));
    localStorage.setItem('mcp-tools', JSON.stringify(tools));
  }, [servers, conversations, models, tools]);

  // Refresca proveedores y servidores (modelos y herramientas)
  const refreshServersAndModels = async () => {
    toast.info("Refrescando proveedores, servidores y modelos...");
    try {
      // Refresca modelos de proveedores API
      for (const provider of providers) {
        await fetchProviderModels(provider.id);
      }
      // Refresca servidores MCP
      const supabaseServers = await loadServersFromSupabase();
      setServers(supabaseServers);
      for (const server of supabaseServers) {
        if (server.isActive) {
          await connectToServer(server.id);
          await fetchModels(server.id);
          await fetchTools(server.id);
        }
      }
      toast.success("Proveedores, servidores y modelos actualizados");
    } catch (error) {
      toast.error("Error al refrescar proveedores/servidores/modelos");
    }
  };

  const fetchProviderModels = async (providerId: string) => {
    const provider = providers.find(p => p.id === providerId);
    if (!provider) return;
    // Simula fetch real, podrías expandir para llamar a la API si tienes key/config
    const fetchedModels: LLMModel[] = (provider.models || []).map(modelId => ({
      id: `${provider.id}-${modelId}`,
      name: modelId,
      provider: provider.id,
      isAvailable: true,
      modelId,
      contextLength: 4096,
      capabilities: {
        vision: false,
        audio: false,
        streaming: true,
        function_calling: false
      }
    }));
    setModels(prevModels => {
      // Elimina modelos antiguos de este proveedor
      const filtered = prevModels.filter(model => model.provider !== provider.id);
      return [...filtered, ...fetchedModels];
    });
  };

  const addServer = async (server: Omit<MCPServer, "id">) => {
    try {
      const newServer = {
        ...server,
        id: crypto.randomUUID()
      };
      
      const success = await saveServerToSupabase(newServer);
      if (success) {
        setServers(prevServers => [...prevServers, newServer]);
        toast.success(`Server "${server.name}" added successfully`);
        return true;
      } else {
        toast.error(`Failed to add server "${server.name}"`);
        return false;
      }
    } catch (error) {
      console.error("Error adding server:", error);
      toast.error(`Error adding server: ${error}`);
      return false;
    }
  };

  const removeServer = async (serverId: string) => {
    try {
      const serverToDelete = servers.find(s => s.id === serverId);
      if (!serverToDelete) {
        toast.error("Server not found");
        return false;
      }

      const success = await deleteServerFromSupabase(serverId);
      if (success) {
        // Disconnect from the server if it's connected
        if (connections.find(conn => conn.serverId === serverId && conn.status === 'connected')) {
          await disconnectFromServer(serverId);
        }
        
        // Remove the server from state
        setServers(prevServers => prevServers.filter(s => s.id !== serverId));
        
        // Remove all tools associated with this server
        setTools(prevTools => prevTools.filter(t => t.serverId !== serverId));
        
        toast.success(`Server "${serverToDelete.name}" deleted successfully`);
        return true;
      } else {
        toast.error(`Failed to delete server "${serverToDelete.name}"`);
        return false;
      }
    } catch (error) {
      console.error("Error removing server:", error);
      toast.error(`Error removing server: ${error}`);
      return false;
    }
  };

  const updateServer = async (updatedServer: MCPServer) => {
    try {
      const success = await saveServerToSupabase(updatedServer);
      if (success) {
        setServers(prevServers => 
          prevServers.map(s => s.id === updatedServer.id ? updatedServer : s)
        );
        toast.success(`Server "${updatedServer.name}" updated successfully`);
        return true;
      } else {
        toast.error(`Failed to update server "${updatedServer.name}"`);
        return false;
      }
    } catch (error) {
      console.error("Error updating server:", error);
      toast.error(`Error updating server: ${error}`);
      return false;
    }
  };

  // Toggle server active status (multiple servers can be active)
  const toggleServerActive = async (serverId: string, isActive: boolean) => {
    try {
      toast.info(isActive ? "Activando servidor..." : "Desactivando servidor...");
      const success = await updateServerActiveStatus(serverId, isActive);
      if (!success) {
        toast.error("No se pudo actualizar el estado del servidor en la base de datos.");
        return false;
      }

      // Refresca la lista de servidores desde Supabase para asegurar sincronía
      const supabaseServers = await loadServersFromSupabase();
      setServers(supabaseServers);
      const refreshedServer = supabaseServers.find(s => s.id === serverId);
      if (!refreshedServer) {
        toast.error("Servidor no encontrado tras refresco.");
        return false;
      }
      if (refreshedServer.isActive) {
        toast.success(`Servidor "${refreshedServer.name}" activado. Intentando conectar...`);
        const connected = await connectToServer(serverId);
        if (connected) {
          toast.success(`Conectado a ${refreshedServer.name}`);
          await fetchModels(serverId, refreshedServer);
          await fetchTools(serverId, refreshedServer);
        } else {
          toast.error(`No se pudo conectar a ${refreshedServer.name}. Verifica que el backend MCP está corriendo y accesible.`);
          return false;
        }
      } else {
        // Si se desactiva, desconecta si corresponde
        if (connections.find(conn => conn.serverId === serverId && conn.status === 'connected')) {
          await disconnectFromServer(serverId);
          toast.success(`Servidor "${refreshedServer.name}" desactivado y desconectado.`);
        } else {
          toast.success(`Servidor "${refreshedServer.name}" desactivado.`);
        }
      }
      return true;
    } catch (error) {
      console.error("Error toggling server active status:", error);
      toast.error(`Error actualizando estado del servidor: ${error instanceof Error ? error.message : error}`);
      return false;
    }
  };

  const setActiveModel = (id: string | null) => {
    if (!id) {
      setActiveModelState(null);
      return;
    }
    const model = models.find(m => m.id === id);
    setActiveModelState(model || null);
  };

  const setActiveConversation = (id: string | null) => {
    if (!id) {
      setActiveConversationState(null);
      return;
    }
    const conversation = conversations.find(c => c.id === id);
    setActiveConversationState(conversation || null);

    // Set the active model based on the conversation
    if (conversation) {
      setActiveModel(conversation.modelId);
    }
  };

  // Modified to not require a specific server
  const createConversation = (modelId: string, title = "New Conversation") => {
    // Permite crear conversación aunque no haya servidores MCP activos, si hay modelos de proveedor
    const model = models.find(m => m.id === modelId);
    if (!model) throw new Error("Modelo no disponible");
    const newConversation: Conversation = {
      id: crypto.randomUUID(),
      title,
      modelId,
      selectedServerId: "", // No se asocia a un server específico
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    setConversations(prevConversations => [...prevConversations, newConversation]);
    setActiveConversationState(newConversation);
    return newConversation;
  };

  const connectToServer = async (serverId: string): Promise<boolean> => {
    const server = servers.find(s => s.id === serverId);
    if (!server) {
      toast.error("Server not found");
      return false;
    }
    
    try {
      // Update connection status to connecting
      setConnections(prev => [
        ...prev.filter(conn => conn.serverId !== serverId),
        { serverId, status: 'connecting' }
      ]);
      
      let connected = false;
      
      if (server.connectionType === 'sse') {
        // Connect via SSE
        const eventSource = await connectSSE(
          server,
          (event) => {
            console.log(`SSE event from ${server.name}:`, event.data);
            // Process incoming SSE events here
          },
          (error) => {
            console.error(`SSE error from ${server.name}:`, error);
            setConnections(prev => prev.map(conn => 
              conn.serverId === serverId 
                ? { ...conn, status: 'error', error: 'Connection error' } 
                : conn
            ));
          },
          () => {
            setConnections(prev => prev.map(conn => 
              conn.serverId === serverId 
                ? { 
                    ...conn, 
                    status: 'connected', 
                    lastConnected: Date.now(),
                    error: undefined,
                    stream: eventSource
                  } 
                : conn
            ));
            toast.success(`Connected to ${server.name}`);
          }
        );
        
        connected = true;
      } else if (server.connectionType === 'stdio') {
        // Connect via STDIO
        connected = await connectSTDIO(server);
        
        if (connected) {
          setConnections(prev => prev.map(conn => 
            conn.serverId === serverId 
              ? { 
                  ...conn, 
                  status: 'connected', 
                  lastConnected: Date.now(),
                  error: undefined
                } 
              : conn
          ));
          toast.success(`Connected to ${server.name}`);
        } else {
          setConnections(prev => prev.map(conn => 
            conn.serverId === serverId 
              ? { ...conn, status: 'error', error: 'Failed to connect' } 
              : conn
          ));
        }
      }
      
      return connected;
    } catch (error) {
      console.error(`Failed to connect to server ${server.name}:`, error);
      setConnections(prev => [
        ...prev.filter(conn => conn.serverId !== serverId),
        { 
          serverId, 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        }
      ]);
      toast.error(`Failed to connect to ${server.name}`);
      return false;
    }
  };

  const disconnectFromServer = (serverId: string) => {
    const connection = connections.find(conn => conn.serverId === serverId);
    if (!connection) return;
    
    try {
      if (connection.stream) {
        connection.stream.close();
      }
      
      setConnections(prev => prev.map(conn => 
        conn.serverId === serverId 
          ? { ...conn, status: 'disconnected', stream: null, process: null } 
          : conn
      ));
      
      console.log(`Disconnected from server ${serverId}`);
    } catch (error) {
      console.error(`Error disconnecting from server ${serverId}:`, error);
    }
  };

  // Updated to use all active servers
  // Nuevo: envía mensajes según la fuente del modelo (proveedor API o servidor MCP)
  const sendMessage = async (content: string) => {
    if (!activeConversation || !activeModel) {
      throw new Error("No active conversation or model selected");
    }

    // Mensaje del usuario
    const userMessage: ChatMessage = {
      id: crypto.randomUUID(),
      role: 'user',
      content,
      timestamp: Date.now(),
    };

    // Actualiza conversación con el mensaje del usuario
    const updatedConversation: Conversation = {
      ...activeConversation,
      messages: [...activeConversation.messages, userMessage],
      updatedAt: Date.now()
    };
    setConversations(prevConversations => 
      prevConversations.map(c => 
        c.id === activeConversation.id ? updatedConversation : c
      )
    );
    setActiveConversationState(updatedConversation);

    // Prepara historial
    const messageHistory = activeConversation.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
    messageHistory.push({ role: 'user', content });

    // Placeholder para la respuesta
    const assistantPlaceholder: ChatMessage = {
      id: crypto.randomUUID(),
      role: 'assistant',
      content: '',
      timestamp: Date.now(),
    };
    const inProgressConversation: Conversation = {
      ...updatedConversation,
      messages: [...updatedConversation.messages, assistantPlaceholder],
      updatedAt: Date.now()
    };
    setConversations(prevConversations => 
      prevConversations.map(c => 
        c.id === activeConversation.id ? inProgressConversation : c
      )
    );
    setActiveConversationState(inProgressConversation);

    // Determina la fuente del modelo (proveedor API o servidor MCP)
    const provider = providers.find(p => p.id === activeModel.provider);
    let responseContent = '';
    try {
      if (provider) {
        // Lógica de proveedor API: simula respuesta o implementa llamada real
        responseContent = `Simulación: respuesta del modelo ${activeModel.name} de ${provider.name}`;
      } else {
        // Busca el servidor MCP correspondiente
        const server = servers.find(s => s.isActive && activeModel.provider === s.id);
        if (!server) throw new Error("No hay servidor MCP activo para este modelo");
        // Aquí puedes usar sendMessageToServer como antes
        responseContent = `Simulación: respuesta del modelo ${activeModel.name} desde el servidor MCP ${server.name}`;
      }
      // Actualiza la conversación con la respuesta
      const finalAssistantMessage: ChatMessage = {
        ...assistantPlaceholder,
        content: responseContent,
      };
      const finalConversation: Conversation = {
        ...updatedConversation,
        messages: [...updatedConversation.messages, finalAssistantMessage],
        updatedAt: Date.now()
      };
      setConversations(prevConversations => 
        prevConversations.map(c => 
          c.id === activeConversation.id ? finalConversation : c
        )
      );
      setActiveConversationState(finalConversation);
    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage: ChatMessage = {
        id: crypto.randomUUID(),
        role: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
      };
      const errorConversation: Conversation = {
        ...updatedConversation,
        messages: [...updatedConversation.messages, errorMessage],
        updatedAt: Date.now()
      };
      setConversations(prevConversations => 
        prevConversations.map(c => 
          c.id === activeConversation.id ? errorConversation : c
        )
      );
      setActiveConversationState(errorConversation);
    }
  };


  const fetchModels = async (serverId: string, serverOverride?: MCPServer) => {
    const server = serverOverride || servers.find(s => s.id === serverId);
    if (!server) {
      toast.error("Server not found");
      return;
    }
    
    try {
      // Fetch real models from the server
      const fetchedModels = await fetchModelsFromServer(server);
      
      if (fetchedModels.length > 0) {
        console.log(`Fetched ${fetchedModels.length} models from ${server.name}:`, fetchedModels);
        
        // Update our models list
        setModels(prevModels => {
          const currentModels = [...prevModels];
          
          // Add new models and update existing ones
          fetchedModels.forEach(model => {
            const existingModelIndex = currentModels.findIndex(_m => _m.id === model.id);
            if (existingModelIndex >= 0) {
              // Update existing model
              currentModels[existingModelIndex] = model;
            } else {
              // Add new model
              currentModels.push(model);
            }
          });
          
          return currentModels;
        });
        
        toast.success(`Loaded ${fetchedModels.length} models from ${server.name}`);
      } else {
        toast.warning(`No models found on server ${server.name}`);
      }
    } catch (error) {
      console.error(`Error fetching models for ${server.name}:`, error);
      toast.error(`Failed to fetch models from ${server.name}`);
    }
  };

  const fetchTools = async (serverId: string, serverOverride?: MCPServer) => {
    const server = serverOverride || servers.find(s => s.id === serverId);
    if (!server) {
      toast.error("Server not found");
      return;
    }
    
    try {
      // Fetch real tools from the server
      const fetchedTools = await fetchToolsFromServer(server);
      
      if (fetchedTools.length > 0) {
        console.log(`Fetched ${fetchedTools.length} tools from ${server.name}:`, fetchedTools);
        
        // Update our tools list
        setTools(prevTools => {
          // Filter out tools for this server
          const otherTools = prevTools.filter(t => t.serverId !== serverId);
          
          // Add the new tools
          return [...otherTools, ...fetchedTools];
        });
        
        toast.success(`Loaded ${fetchedTools.length} tools from ${server.name}`);
      } else {
        toast.warning(`No tools found on server ${server.name}`);
      }
    } catch (error) {
      console.error(`Error fetching tools for ${server.name}:`, error);
      toast.error(`Failed to fetch tools from ${server.name}`);
    }
  };
  
  const updateProviderConfig = (providerId: string, config: Partial<LLMProvider>) => {
    setProviders(prev => prev.map(provider => 
      provider.id === providerId ? { ...provider, ...config } : provider
    ));
  };
  
  const getProviderById = (id: string) => {
    return providers.find(provider => provider.id === id);
  };

  return (
    <MCPContext.Provider
      value={{
        servers,
        providers,
        models,
        tools,
        conversations,
        activeConversation,
        activeModel,
        refreshServersAndModels,
        activeServers,
        activeServerIds,
        connections,
        addServer,
        removeServer,
        updateServer,
        toggleServerActive,
        setActiveModel,
        setActiveConversation,
        createConversation,
        sendMessage,
        fetchModels,
        fetchTools,
        connectToServer,
        disconnectFromServer,
        updateProviderConfig,
        getProviderById,
      }}
    >
      {children}
    </MCPContext.Provider>
  );
}

export function useMCP() {
  const context = useContext(MCPContext);
  if (!context) {
    throw new Error("useMCP must be used within an MCPProvider");
  }
  return context;
}

