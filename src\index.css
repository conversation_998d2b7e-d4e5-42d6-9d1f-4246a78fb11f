
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 205 100% 33%; /* Grupo SAI Blue #005BA7 */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 205 90% 95%;
    --accent-foreground: 205 100% 33%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 205 100% 33%;

    --radius: 0.75rem;

    --sidebar-background: 204 100% 98%;
    --sidebar-foreground: 205 100% 33%;
    --sidebar-primary: 205 100% 33%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 204 100% 94%;
    --sidebar-accent-foreground: 205 100% 33%;
    --sidebar-border: 204 60% 90%;
    --sidebar-ring: 205 100% 50%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 205 100% 65%; /* Lighter Grupo SAI Blue for dark mode */
    --primary-foreground: 222 47% 11%;

    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 32% 20%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 212.7 26% 83%;
    
    --sidebar-background: 215 28% 17%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 205 100% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 28% 25%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 28% 25%;
    --sidebar-ring: 205 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator .dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
  margin: 0 2px;
  animation: pulse 1.5s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 50%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  25% {
    transform: scale(1.5);
    opacity: 1;
  }
}

/* Code block styling */
pre {
  @apply bg-muted rounded-lg p-4 overflow-x-auto my-4 font-mono text-sm;
}

code {
  @apply font-mono text-sm bg-muted px-1 py-0.5 rounded;
}

/* Animation classes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from { 
    transform: translateY(20px);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from { 
    transform: translateX(20px);
    opacity: 0;
  }
  to { 
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
  100% { transform: translateY(0px); }
}

@keyframes shine {
  from {
    background-position: -200% center;
  }
  to {
    background-position: 200% center;
  }
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius);
}

.dark .glass {
  background: rgba(25, 25, 35, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Animated elements */
.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.slide-in-up {
  animation: slideInUp 0.5s ease forwards;
}

.slide-in-right {
  animation: slideInRight 0.5s ease forwards;
}

.float {
  animation: float 6s ease-in-out infinite;
}

/* Message animations */
.message-appear {
  opacity: 0;
  transform: translateY(10px);
  animation: slideInUp 0.3s ease forwards;
}

/* Hover effects */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.03);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  @apply shadow-md;
  box-shadow: 0 0 15px rgba(var(--primary), 0.3);
}

.dark .hover-glow:hover {
  box-shadow: 0 0 15px rgba(var(--primary), 0.15);
}

/* Gradient button */
.gradient-btn {
  background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--primary)/0.8));
  background-size: 200% 200%;
  animation: shine 3s linear infinite;
  transition: all 0.3s ease;
}

.gradient-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 91, 167, 0.4);
}

.dark .gradient-btn {
  background: linear-gradient(45deg, hsl(var(--primary)/0.8), hsl(var(--primary)/0.6));
}

.dark .gradient-btn:hover {
  box-shadow: 0 5px 15px rgba(125, 211, 252, 0.2);
}
