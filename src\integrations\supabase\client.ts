
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://gwvfrsforsbagoiktymr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd3dmZyc2ZvcnNiYWdvaWt0eW1yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg1OTY1MjMsImV4cCI6MjA1NDE3MjUyM30.XpQ7My8UOlx9hLyOBwlVn2aZQG8QHM2ePqmO3DYOKHo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
