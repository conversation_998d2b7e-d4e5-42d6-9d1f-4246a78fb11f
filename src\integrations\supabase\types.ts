export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      app_settings: {
        Row: {
          created_at: string
          id: number
          key: string
          updated_at: string
          value: <PERSON><PERSON>
        }
        Insert: {
          created_at?: string
          id?: number
          key: string
          updated_at?: string
          value: Json
        }
        Update: {
          created_at?: string
          id?: number
          key?: string
          updated_at?: string
          value?: Json
        }
        Relationships: []
      }
      chat_aaa_servers: {
        Row: {
          connection_type: string
          created_at: string
          id: string
          is_active: boolean
          name: string
          path: string | null
          stdio_config: Json | null
          updated_at: string
          url: string | null
        }
        Insert: {
          connection_type: string
          created_at?: string
          id?: string
          is_active?: boolean
          name: string
          path?: string | null
          stdio_config?: Json | null
          updated_at?: string
          url?: string | null
        }
        Update: {
          connection_type?: string
          created_at?: string
          id?: string
          is_active?: boolean
          name?: string
          path?: string | null
          stdio_config?: Json | null
          updated_at?: string
          url?: string | null
        }
        Relationships: []
      }
      chats_messages: {
        Row: {
          content: string
          id: string
          is_image: boolean
          sender: string
          timestamp: string
        }
        Insert: {
          content: string
          id?: string
          is_image?: boolean
          sender: string
          timestamp?: string
        }
        Update: {
          content?: string
          id?: string
          is_image?: boolean
          sender?: string
          timestamp?: string
        }
        Relationships: []
      }
      clerk_amortization_schedules: {
        Row: {
          accumulated_insurance: number | null
          accumulated_interest: number | null
          applicant_id: string | null
          balance_with_insurance: number | null
          balance_without_insurance: number | null
          created_at: string | null
          current_interest: number | null
          financed_insurance: number | null
          id: string
          payment_number: number | null
          principal_payment_with_insurance: number | null
          principal_payment_without_insurance: number | null
        }
        Insert: {
          accumulated_insurance?: number | null
          accumulated_interest?: number | null
          applicant_id?: string | null
          balance_with_insurance?: number | null
          balance_without_insurance?: number | null
          created_at?: string | null
          current_interest?: number | null
          financed_insurance?: number | null
          id?: string
          payment_number?: number | null
          principal_payment_with_insurance?: number | null
          principal_payment_without_insurance?: number | null
        }
        Update: {
          accumulated_insurance?: number | null
          accumulated_interest?: number | null
          applicant_id?: string | null
          balance_with_insurance?: number | null
          balance_without_insurance?: number | null
          created_at?: string | null
          current_interest?: number | null
          financed_insurance?: number | null
          id?: string
          payment_number?: number | null
          principal_payment_with_insurance?: number | null
          principal_payment_without_insurance?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "clerk_amortization_schedules_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicant_summary"
            referencedColumns: ["applicant_id"]
          },
          {
            foreignKeyName: "clerk_amortization_schedules_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicants"
            referencedColumns: ["id"]
          },
        ]
      }
      clerk_applicants: {
        Row: {
          age: number | null
          application_date: string | null
          birth_date: string | null
          company_name: string | null
          company_nit: string | null
          created_at: string | null
          credit_line: string | null
          effective_rate: number | null
          financial_entity: string | null
          full_name: string
          guarantee: string | null
          id: string
          id_number: string | null
          insurance_financed: boolean | null
          insurance_term: number | null
          insurance_total_value: number | null
          insurance_type: string | null
          loan_amount: number | null
          loan_term: number | null
          monthly_payment: number | null
          nominal_rate: number | null
          regime: string | null
          user_id: string | null
        }
        Insert: {
          age?: number | null
          application_date?: string | null
          birth_date?: string | null
          company_name?: string | null
          company_nit?: string | null
          created_at?: string | null
          credit_line?: string | null
          effective_rate?: number | null
          financial_entity?: string | null
          full_name: string
          guarantee?: string | null
          id?: string
          id_number?: string | null
          insurance_financed?: boolean | null
          insurance_term?: number | null
          insurance_total_value?: number | null
          insurance_type?: string | null
          loan_amount?: number | null
          loan_term?: number | null
          monthly_payment?: number | null
          nominal_rate?: number | null
          regime?: string | null
          user_id?: string | null
        }
        Update: {
          age?: number | null
          application_date?: string | null
          birth_date?: string | null
          company_name?: string | null
          company_nit?: string | null
          created_at?: string | null
          credit_line?: string | null
          effective_rate?: number | null
          financial_entity?: string | null
          full_name?: string
          guarantee?: string | null
          id?: string
          id_number?: string | null
          insurance_financed?: boolean | null
          insurance_term?: number | null
          insurance_total_value?: number | null
          insurance_type?: string | null
          loan_amount?: number | null
          loan_term?: number | null
          monthly_payment?: number | null
          nominal_rate?: number | null
          regime?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clerk_applicants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "clerk_users"
            referencedColumns: ["id"]
          },
        ]
      }
      clerk_chat_histories: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      clerk_financial_info: {
        Row: {
          applicant_id: string | null
          available_payment_with_insurance: number | null
          available_payment_without_insurance: number | null
          created_at: string | null
          current_debt_ratio: number | null
          id: string
          income_after_loan: number | null
          legal_deductions: number | null
          monthly_income: number | null
          net_income: number | null
          obligation_deductions: number | null
          other_deductions: number | null
          previous_debt_ratio: number | null
        }
        Insert: {
          applicant_id?: string | null
          available_payment_with_insurance?: number | null
          available_payment_without_insurance?: number | null
          created_at?: string | null
          current_debt_ratio?: number | null
          id?: string
          income_after_loan?: number | null
          legal_deductions?: number | null
          monthly_income?: number | null
          net_income?: number | null
          obligation_deductions?: number | null
          other_deductions?: number | null
          previous_debt_ratio?: number | null
        }
        Update: {
          applicant_id?: string | null
          available_payment_with_insurance?: number | null
          available_payment_without_insurance?: number | null
          created_at?: string | null
          current_debt_ratio?: number | null
          id?: string
          income_after_loan?: number | null
          legal_deductions?: number | null
          monthly_income?: number | null
          net_income?: number | null
          obligation_deductions?: number | null
          other_deductions?: number | null
          previous_debt_ratio?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "clerk_financial_info_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicant_summary"
            referencedColumns: ["applicant_id"]
          },
          {
            foreignKeyName: "clerk_financial_info_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicants"
            referencedColumns: ["id"]
          },
        ]
      }
      clerk_loan_liquidations: {
        Row: {
          adjustment_interest_with_insurance: number | null
          adjustment_interest_without_insurance: number | null
          applicant_id: string | null
          created_at: string | null
          debts_to_consolidate: number | null
          id: string
          insurance_per_million: number | null
          max_amount_with_insurance: number | null
          max_amount_without_insurance: number | null
          payment_per_million: number | null
          term: number | null
        }
        Insert: {
          adjustment_interest_with_insurance?: number | null
          adjustment_interest_without_insurance?: number | null
          applicant_id?: string | null
          created_at?: string | null
          debts_to_consolidate?: number | null
          id?: string
          insurance_per_million?: number | null
          max_amount_with_insurance?: number | null
          max_amount_without_insurance?: number | null
          payment_per_million?: number | null
          term?: number | null
        }
        Update: {
          adjustment_interest_with_insurance?: number | null
          adjustment_interest_without_insurance?: number | null
          applicant_id?: string | null
          created_at?: string | null
          debts_to_consolidate?: number | null
          id?: string
          insurance_per_million?: number | null
          max_amount_with_insurance?: number | null
          max_amount_without_insurance?: number | null
          payment_per_million?: number | null
          term?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "clerk_loan_liquidations_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicant_summary"
            referencedColumns: ["applicant_id"]
          },
          {
            foreignKeyName: "clerk_loan_liquidations_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicants"
            referencedColumns: ["id"]
          },
        ]
      }
      clerk_loan_requests: {
        Row: {
          adjustment_days: number | null
          adjustment_interest: number | null
          applicant_id: string | null
          created_at: string | null
          id: string
          insurance_adjustment_interest: number | null
          monthly_payment: number | null
          net_disbursement: number | null
          nominal_rate_monthly: number | null
          requested_amount: number | null
          requested_term: number | null
          total_cost_rate_monthly: number | null
          upfront_insurance: number | null
        }
        Insert: {
          adjustment_days?: number | null
          adjustment_interest?: number | null
          applicant_id?: string | null
          created_at?: string | null
          id?: string
          insurance_adjustment_interest?: number | null
          monthly_payment?: number | null
          net_disbursement?: number | null
          nominal_rate_monthly?: number | null
          requested_amount?: number | null
          requested_term?: number | null
          total_cost_rate_monthly?: number | null
          upfront_insurance?: number | null
        }
        Update: {
          adjustment_days?: number | null
          adjustment_interest?: number | null
          applicant_id?: string | null
          created_at?: string | null
          id?: string
          insurance_adjustment_interest?: number | null
          monthly_payment?: number | null
          net_disbursement?: number | null
          nominal_rate_monthly?: number | null
          requested_amount?: number | null
          requested_term?: number | null
          total_cost_rate_monthly?: number | null
          upfront_insurance?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "clerk_loan_requests_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicant_summary"
            referencedColumns: ["applicant_id"]
          },
          {
            foreignKeyName: "clerk_loan_requests_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicants"
            referencedColumns: ["id"]
          },
        ]
      }
      clerk_observations: {
        Row: {
          applicant_id: string | null
          created_at: string | null
          effective_rate_annual: number | null
          id: string
          insurance_adjustment_factor: number | null
          insurance_factor: number | null
          minimum_wage: number | null
          monthly_insurance: number | null
          nominal_rate_annual: number | null
          payment_variation: number | null
        }
        Insert: {
          applicant_id?: string | null
          created_at?: string | null
          effective_rate_annual?: number | null
          id?: string
          insurance_adjustment_factor?: number | null
          insurance_factor?: number | null
          minimum_wage?: number | null
          monthly_insurance?: number | null
          nominal_rate_annual?: number | null
          payment_variation?: number | null
        }
        Update: {
          applicant_id?: string | null
          created_at?: string | null
          effective_rate_annual?: number | null
          id?: string
          insurance_adjustment_factor?: number | null
          insurance_factor?: number | null
          minimum_wage?: number | null
          monthly_insurance?: number | null
          nominal_rate_annual?: number | null
          payment_variation?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "clerk_observations_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicant_summary"
            referencedColumns: ["applicant_id"]
          },
          {
            foreignKeyName: "clerk_observations_applicant_id_fkey"
            columns: ["applicant_id"]
            isOneToOne: false
            referencedRelation: "clerk_applicants"
            referencedColumns: ["id"]
          },
        ]
      }
      clerk_reference_data: {
        Row: {
          created_at: string | null
          credit_line: string | null
          delinquency_status: string | null
          guarantee: string | null
          id: string
          insurance_type: string | null
          interest_rate: number | null
          regime: string | null
          term: number | null
        }
        Insert: {
          created_at?: string | null
          credit_line?: string | null
          delinquency_status?: string | null
          guarantee?: string | null
          id?: string
          insurance_type?: string | null
          interest_rate?: number | null
          regime?: string | null
          term?: number | null
        }
        Update: {
          created_at?: string | null
          credit_line?: string | null
          delinquency_status?: string | null
          guarantee?: string | null
          id?: string
          insurance_type?: string | null
          interest_rate?: number | null
          regime?: string | null
          term?: number | null
        }
        Relationships: []
      }
      clerk_users: {
        Row: {
          clerk_user_id: string
          created_at: string | null
          email: string | null
          id: string
        }
        Insert: {
          clerk_user_id: string
          created_at?: string | null
          email?: string | null
          id?: string
        }
        Update: {
          clerk_user_id?: string
          created_at?: string | null
          email?: string | null
          id?: string
        }
        Relationships: []
      }
      clientes: {
        Row: {
          celular: string
          nombre_cliente: string
        }
        Insert: {
          celular: string
          nombre_cliente: string
        }
        Update: {
          celular?: string
          nombre_cliente?: string
        }
        Relationships: []
      }
      configuraciones: {
        Row: {
          created_at: string | null
          elevenlabs_agent_id: string
          elevenlabs_api_key: string
          elevenlabs_voice_id: string
          id: string
          tasa_cambio_dolar_boliviano: number
          twilio_account_sid: string | null
          twilio_auth_token: string | null
          twilio_phone_number: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          elevenlabs_agent_id?: string
          elevenlabs_api_key: string
          elevenlabs_voice_id?: string
          id?: string
          tasa_cambio_dolar_boliviano?: number
          twilio_account_sid?: string | null
          twilio_auth_token?: string | null
          twilio_phone_number?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          elevenlabs_agent_id?: string
          elevenlabs_api_key?: string
          elevenlabs_voice_id?: string
          id?: string
          tasa_cambio_dolar_boliviano?: number
          twilio_account_sid?: string | null
          twilio_auth_token?: string | null
          twilio_phone_number?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      cuentas_ahorro: {
        Row: {
          celular_cliente: string
          cuenta_ahorro: string
          id: number
          moneda: string
          saldo: number
        }
        Insert: {
          celular_cliente: string
          cuenta_ahorro: string
          id?: number
          moneda: string
          saldo: number
        }
        Update: {
          celular_cliente?: string
          cuenta_ahorro?: string
          id?: number
          moneda?: string
          saldo?: number
        }
        Relationships: [
          {
            foreignKeyName: "cuentas_ahorro_celular_cliente_fkey"
            columns: ["celular_cliente"]
            isOneToOne: false
            referencedRelation: "clientes"
            referencedColumns: ["celular"]
          },
        ]
      }
      documents: {
        Row: {
          content: string | null
          embedding: string | null
          id: number
          metadata: Json | null
        }
        Insert: {
          content?: string | null
          embedding?: string | null
          id?: number
          metadata?: Json | null
        }
        Update: {
          content?: string | null
          embedding?: string | null
          id?: number
          metadata?: Json | null
        }
        Relationships: []
      }
      documents_juridico: {
        Row: {
          content: string | null
          embedding: string | null
          id: number
          metadata: Json | null
        }
        Insert: {
          content?: string | null
          embedding?: string | null
          id?: number
          metadata?: Json | null
        }
        Update: {
          content?: string | null
          embedding?: string | null
          id?: number
          metadata?: Json | null
        }
        Relationships: []
      }
      invoice_items: {
        Row: {
          description: string
          id: number
          invoice_id: number | null
          quantity: number
          total_price: number
          unit_price: number
        }
        Insert: {
          description: string
          id?: number
          invoice_id?: number | null
          quantity: number
          total_price: number
          unit_price: number
        }
        Update: {
          description?: string
          id?: number
          invoice_id?: number | null
          quantity?: number
          total_price?: number
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          billing_city: string
          billing_country: string
          billing_name: string
          billing_state: string | null
          billing_street: string
          billing_zip_code: string | null
          date: string
          estado: string | null
          file: string | null
          id: number
          invoice_number: string
          subtotal: number
          tax: number
          total: number
        }
        Insert: {
          billing_city: string
          billing_country: string
          billing_name: string
          billing_state?: string | null
          billing_street: string
          billing_zip_code?: string | null
          date: string
          estado?: string | null
          file?: string | null
          id?: number
          invoice_number: string
          subtotal: number
          tax: number
          total: number
        }
        Update: {
          billing_city?: string
          billing_country?: string
          billing_name?: string
          billing_state?: string | null
          billing_street?: string
          billing_zip_code?: string | null
          date?: string
          estado?: string | null
          file?: string | null
          id?: number
          invoice_number?: string
          subtotal?: number
          tax?: number
          total?: number
        }
        Relationships: []
      }
      n8n_chat_chistes: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      n8n_chat_congreso: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      n8n_chat_grabaciones: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      n8n_chat_histories: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      n8n_chat_histories_vector: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      n8n_chat_odontogramas: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      n8n_chat_web: {
        Row: {
          id: number
          message: Json
          session_id: string
        }
        Insert: {
          id?: number
          message: Json
          session_id: string
        }
        Update: {
          id?: number
          message?: Json
          session_id?: string
        }
        Relationships: []
      }
      odontogramas: {
        Row: {
          created_at: string
          datos: Json
          fecha: string
          id: string
          nombre: string
          paciente_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          datos: Json
          fecha?: string
          id?: string
          nombre: string
          paciente_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          datos?: Json
          fecha?: string
          id?: string
          nombre?: string
          paciente_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "odontogramas_paciente_id_fkey"
            columns: ["paciente_id"]
            isOneToOne: false
            referencedRelation: "pacientes"
            referencedColumns: ["id"]
          },
        ]
      }
      pacientes: {
        Row: {
          created_at: string
          email: string | null
          fecha_nacimiento: string | null
          id: string
          identificacion: string
          nombre: string
          telefono: string | null
        }
        Insert: {
          created_at?: string
          email?: string | null
          fecha_nacimiento?: string | null
          id?: string
          identificacion: string
          nombre: string
          telefono?: string | null
        }
        Update: {
          created_at?: string
          email?: string | null
          fecha_nacimiento?: string | null
          id?: string
          identificacion?: string
          nombre?: string
          telefono?: string | null
        }
        Relationships: []
      }
      paquetes: {
        Row: {
          paquete: string
          precio: number
          vencimiento: string
        }
        Insert: {
          paquete: string
          precio: number
          vencimiento: string
        }
        Update: {
          paquete?: string
          precio?: number
          vencimiento?: string
        }
        Relationships: []
      }
      pollo_pedido_items: {
        Row: {
          cantidad: number
          created_at: string | null
          id: string
          nombre: string
          pedido_id: string
          precio: number
        }
        Insert: {
          cantidad: number
          created_at?: string | null
          id?: string
          nombre: string
          pedido_id: string
          precio: number
        }
        Update: {
          cantidad?: number
          created_at?: string | null
          id?: string
          nombre?: string
          pedido_id?: string
          precio?: number
        }
        Relationships: [
          {
            foreignKeyName: "pollo_pedido_items_pedido_id_fkey"
            columns: ["pedido_id"]
            isOneToOne: false
            referencedRelation: "pollo_pedidos"
            referencedColumns: ["id"]
          },
        ]
      }
      pollo_pedidos: {
        Row: {
          created_at: string | null
          estado: string
          id: string
          nombre_cliente: string
          numero_pedido: number
          total: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          estado: string
          id?: string
          nombre_cliente: string
          numero_pedido?: number
          total?: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          estado?: string
          id?: string
          nombre_cliente?: string
          numero_pedido?: number
          total?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      pollo_productos: {
        Row: {
          categoria: string
          codigo: string
          created_at: string | null
          descripcion: string | null
          disponible: boolean | null
          id: string
          nombre: string
          precio: number
          updated_at: string | null
        }
        Insert: {
          categoria: string
          codigo: string
          created_at?: string | null
          descripcion?: string | null
          disponible?: boolean | null
          id?: string
          nombre: string
          precio: number
          updated_at?: string | null
        }
        Update: {
          categoria?: string
          codigo?: string
          created_at?: string | null
          descripcion?: string | null
          disponible?: boolean | null
          id?: string
          nombre?: string
          precio?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      productos: {
        Row: {
          codigo_barras: string
          costo: number
          id: number
          nombre_producto: string
          precio: number
        }
        Insert: {
          codigo_barras: string
          costo: number
          id?: number
          nombre_producto: string
          precio: number
        }
        Update: {
          codigo_barras?: string
          costo?: number
          id?: number
          nombre_producto?: string
          precio?: number
        }
        Relationships: []
      }
      rut_contribuyentes: {
        Row: {
          actividad_principal: string | null
          actividad_secundaria: string | null
          cargo_representante: string | null
          ciudad: string | null
          codigo_actividad_principal: string | null
          codigo_actividad_secundaria: string | null
          codigo_postal: string | null
          correo_electronico: string | null
          departamento: string | null
          direccion_principal: string | null
          dv: string | null
          establecimientos: Json | null
          estado: string | null
          fecha_expedicion: string | null
          fecha_generacion_documento: string | null
          fecha_inicio_actividad: string | null
          id: number
          nit: string
          nombre_comercial: string | null
          numero_identificacion: string | null
          pais: string | null
          razon_social: string | null
          representante_legal: string | null
          responsabilidades: string | null
          sigla: string | null
          telefono: string | null
          telefono2: string | null
          tipo_contribuyente: string | null
          tipo_documento: string | null
        }
        Insert: {
          actividad_principal?: string | null
          actividad_secundaria?: string | null
          cargo_representante?: string | null
          ciudad?: string | null
          codigo_actividad_principal?: string | null
          codigo_actividad_secundaria?: string | null
          codigo_postal?: string | null
          correo_electronico?: string | null
          departamento?: string | null
          direccion_principal?: string | null
          dv?: string | null
          establecimientos?: Json | null
          estado?: string | null
          fecha_expedicion?: string | null
          fecha_generacion_documento?: string | null
          fecha_inicio_actividad?: string | null
          id?: number
          nit: string
          nombre_comercial?: string | null
          numero_identificacion?: string | null
          pais?: string | null
          razon_social?: string | null
          representante_legal?: string | null
          responsabilidades?: string | null
          sigla?: string | null
          telefono?: string | null
          telefono2?: string | null
          tipo_contribuyente?: string | null
          tipo_documento?: string | null
        }
        Update: {
          actividad_principal?: string | null
          actividad_secundaria?: string | null
          cargo_representante?: string | null
          ciudad?: string | null
          codigo_actividad_principal?: string | null
          codigo_actividad_secundaria?: string | null
          codigo_postal?: string | null
          correo_electronico?: string | null
          departamento?: string | null
          direccion_principal?: string | null
          dv?: string | null
          establecimientos?: Json | null
          estado?: string | null
          fecha_expedicion?: string | null
          fecha_generacion_documento?: string | null
          fecha_inicio_actividad?: string | null
          id?: number
          nit?: string
          nombre_comercial?: string | null
          numero_identificacion?: string | null
          pais?: string | null
          razon_social?: string | null
          representante_legal?: string | null
          responsabilidades?: string | null
          sigla?: string | null
          telefono?: string | null
          telefono2?: string | null
          tipo_contribuyente?: string | null
          tipo_documento?: string | null
        }
        Relationships: []
      }
      supermarket_sales: {
        Row: {
          branch: string | null
          city: string | null
          cogs: number | null
          customer_type: string | null
          date: string | null
          gender: string | null
          gross_income: number | null
          gross_margin_percentage: number | null
          id: number
          invoice_id: string | null
          payment: string | null
          product_line: string | null
          quantity: number | null
          rating: number | null
          tax_5_percent: number | null
          time: string | null
          total: number | null
          unit_price: number | null
        }
        Insert: {
          branch?: string | null
          city?: string | null
          cogs?: number | null
          customer_type?: string | null
          date?: string | null
          gender?: string | null
          gross_income?: number | null
          gross_margin_percentage?: number | null
          id?: number
          invoice_id?: string | null
          payment?: string | null
          product_line?: string | null
          quantity?: number | null
          rating?: number | null
          tax_5_percent?: number | null
          time?: string | null
          total?: number | null
          unit_price?: number | null
        }
        Update: {
          branch?: string | null
          city?: string | null
          cogs?: number | null
          customer_type?: string | null
          date?: string | null
          gender?: string | null
          gross_income?: number | null
          gross_margin_percentage?: number | null
          id?: number
          invoice_id?: string | null
          payment?: string | null
          product_line?: string | null
          quantity?: number | null
          rating?: number | null
          tax_5_percent?: number | null
          time?: string | null
          total?: number | null
          unit_price?: number | null
        }
        Relationships: []
      }
      tehran_house: {
        Row: {
          address: string
          area: number
          elevator: boolean
          id: number
          parking: boolean
          price: number
          price_usd: number
          room: number
          warehouse: boolean
        }
        Insert: {
          address: string
          area: number
          elevator: boolean
          id?: number
          parking: boolean
          price: number
          price_usd: number
          room: number
          warehouse: boolean
        }
        Update: {
          address?: string
          area?: number
          elevator?: boolean
          id?: number
          parking?: boolean
          price?: number
          price_usd?: number
          room?: number
          warehouse?: boolean
        }
        Relationships: []
      }
      utility_bills: {
        Row: {
          bill_number: string
          contract_number: string
          created_at: string | null
          customer_address: string
          customer_city: string
          customer_name: string
          due_date: string
          electronic_payment_number: string | null
          id: string
          issue_date: string
          last_payment_amount: number | null
          last_payment_date: string | null
          status: string | null
          total_amount: number
          updated_at: string | null
          utility_company_id: string | null
        }
        Insert: {
          bill_number: string
          contract_number: string
          created_at?: string | null
          customer_address: string
          customer_city: string
          customer_name: string
          due_date: string
          electronic_payment_number?: string | null
          id?: string
          issue_date: string
          last_payment_amount?: number | null
          last_payment_date?: string | null
          status?: string | null
          total_amount: number
          updated_at?: string | null
          utility_company_id?: string | null
        }
        Update: {
          bill_number?: string
          contract_number?: string
          created_at?: string | null
          customer_address?: string
          customer_city?: string
          customer_name?: string
          due_date?: string
          electronic_payment_number?: string | null
          id?: string
          issue_date?: string
          last_payment_amount?: number | null
          last_payment_date?: string | null
          status?: string | null
          total_amount?: number
          updated_at?: string | null
          utility_company_id?: string | null
        }
        Relationships: []
      }
      utility_charges: {
        Row: {
          concept_name: string
          created_at: string | null
          id: string
          quantity: number | null
          subtotal: number
          unit_value: number | null
          updated_at: string | null
          utility_service_id: string | null
        }
        Insert: {
          concept_name: string
          created_at?: string | null
          id?: string
          quantity?: number | null
          subtotal: number
          unit_value?: number | null
          updated_at?: string | null
          utility_service_id?: string | null
        }
        Update: {
          concept_name?: string
          created_at?: string | null
          id?: string
          quantity?: number | null
          subtotal?: number
          unit_value?: number | null
          updated_at?: string | null
          utility_service_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "utility_charges_utility_service_id_fkey"
            columns: ["utility_service_id"]
            isOneToOne: false
            referencedRelation: "utility_services"
            referencedColumns: ["id"]
          },
        ]
      }
      utility_services: {
        Row: {
          created_at: string | null
          id: string
          service_type: string
          total_amount: number
          updated_at: string | null
          utility_bill_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          service_type: string
          total_amount: number
          updated_at?: string | null
          utility_bill_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          service_type?: string
          total_amount?: number
          updated_at?: string | null
          utility_bill_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "utility_services_utility_bill_id_fkey"
            columns: ["utility_bill_id"]
            isOneToOne: false
            referencedRelation: "utility_bills"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      clerk_applicant_summary: {
        Row: {
          adjustment_days: number | null
          adjustment_interest: number | null
          age: number | null
          applicant_id: string | null
          application_date: string | null
          available_payment_with_insurance: number | null
          available_payment_without_insurance: number | null
          birth_date: string | null
          clerk_user_id: string | null
          company_name: string | null
          company_nit: string | null
          credit_line: string | null
          current_debt_ratio: number | null
          effective_rate: number | null
          effective_rate_annual: number | null
          email: string | null
          financial_entity: string | null
          full_name: string | null
          guarantee: string | null
          id_number: string | null
          income_after_loan: number | null
          insurance_adjustment_interest: number | null
          insurance_factor: number | null
          insurance_financed: boolean | null
          insurance_term: number | null
          insurance_total_value: number | null
          insurance_type: string | null
          legal_deductions: number | null
          loan_amount: number | null
          loan_liquidation_terms: number[] | null
          loan_term: number | null
          max_insurance_per_million: number | null
          max_loan_amount_with_insurance: number | null
          max_loan_amount_without_insurance: number | null
          max_payment_per_million: number | null
          minimum_wage: number | null
          monthly_income: number | null
          monthly_insurance: number | null
          monthly_payment: number | null
          net_disbursement: number | null
          net_income: number | null
          nominal_rate: number | null
          nominal_rate_monthly: number | null
          obligation_deductions: number | null
          other_deductions: number | null
          payment_variation: number | null
          previous_debt_ratio: number | null
          regime: string | null
          requested_amount: number | null
          requested_term: number | null
          sample_balance_with_insurance: number | null
          sample_current_interest: number | null
          total_cost_rate_monthly: number | null
          upfront_insurance: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      handle_pollo_webhook: {
        Args: { request_body: Json }
        Returns: Json
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      l2_norm: {
        Args: { "": unknown } | { "": unknown }
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      match_documents: {
        Args: { query_embedding: string; match_count?: number; filter?: Json }
        Returns: {
          id: number
          content: string
          metadata: Json
          similarity: number
        }[]
      }
      match_documents_juridico: {
        Args: { query_embedding: string; match_count?: number; filter?: Json }
        Returns: {
          id: number
          content: string
          metadata: Json
          similarity: number
        }[]
      }
      obtener_pedido_actual: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      webhook_pollo_pedido: {
        Args: { payload: Json }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
