import { <PERSON><PERSON><PERSON><PERSON>, MCPServer } from "@/types/mcp";

/**
 * <PERSON><PERSON>ly invokes an MCP tool, validating all required fields and logging context.
 * @param toolName Name of the tool to invoke
 * @param args Arguments to pass to the tool
 * @param tools List of available tools
 * @param servers List of active servers
 * @returns Tool execution result or throws an error
 */
export async function invokeMCPTool({
  toolName,
  args,
  tools,
  servers,
}: {
  toolName: string;
  args: any;
  tools: MCPTool[];
  servers: MCPServer[];
}): Promise<any> {
  // Find the tool
  const tool = tools.find(t => t.name === toolName);
  if (!tool) {
    const msg = `Tool '${toolName}' not found in available tools.`;
    console.error(msg, { toolName, tools });
    throw new Error(msg);
  }
  if (!tool.id) {
    const msg = `Tool '${toolName}' is missing an ID.`;
    console.error(msg, { tool });
    throw new Error(msg);
  }

  // Find the server
  const server = servers.find(s => s.id === tool.serverId);
  if (!server) {
    const msg = `Server for tool '${toolName}' (serverId: ${tool.serverId}) not found.`;
    console.error(msg, { tool, servers });
    throw new Error(msg);
  }
  // Soporta servidores HTTP y STDIO
  if (server.connectionType === "http") {
    if (!server.url) {
      const msg = `Server for tool '${toolName}' is missing a URL.`;
      console.error(msg, { server });
      throw new Error(msg);
    }
    // Construct the invocation URL
    const url = `${server.url}/v1/tools/${tool.id}/invoke`;
    console.log(`[invokeMCPTool] Invoking tool (HTTP)`, {
      toolName,
      toolId: tool.id,
      serverUrl: server.url,
      url,
      args
    });
    // Call the tool
    let response;
    try {
      response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(args),
      });
    } catch (err) {
      console.error(`[invokeMCPTool] Network error invoking tool`, { url, err });
      throw new Error(`Network error invoking tool '${toolName}': ${err}`);
    }
    if (!response.ok) {
      const text = await response.text();
      const msg = `Error invoking tool '${toolName}': ${response.status} ${response.statusText} - ${text}`;
      console.error(msg, { url, response });
      throw new Error(msg);
    }
    // Parse and return result
    try {
      const result = await response.json();
      return result;
    } catch (err) {
      console.error(`[invokeMCPTool] Error parsing tool response`, { err });
      throw new Error(`Error parsing tool response: ${err}`);
    }
  } else if (server.connectionType === "stdio") {
    if (!server.path) {
      const msg = `Server for tool '${toolName}' is missing a path for stdio execution.`;
      console.error(msg, { server });
      throw new Error(msg);
    }
    // Invocación STDIO vía backend Express
    try {
      const stdioArgs = server.stdioConfig?.args || [];
      const input = { tool: toolName, args };
      console.log(`[invokeMCPTool] Invocando STDIO vía backend:`, { path: server.path, args: stdioArgs, input });
      const response = await fetch("/api/mcp-stdio", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ path: server.path, args: stdioArgs, input })
      });
      if (!response.ok) {
        const text = await response.text();
        throw new Error(`STDIO backend error: ${response.status} ${response.statusText} - ${text}`);
      }
      // Intenta parsear como JSON, si no, retorna como string
      const text = await response.text();
      try {
        return JSON.parse(text);
      } catch (e) {
        return text;
      }
    } catch (err) {
      console.error(`[invokeMCPTool] STDIO invocation error (frontend/backend split)`, { err, server, tool });
      throw new Error(`STDIO invocation via backend failed for tool '${toolName}': ${err}`);
    }
  } else {
    const msg = `Unknown connection type for server: ${server.connectionType}`;
    console.error(msg, { server });
    throw new Error(msg);
  }
}
