import { MCPServer, MCPCapabilities, ProgressNotification, ElicitationRequest, ElicitationResponse } from "@/types/mcp";

// Modern MCP Client with enhanced capabilities
export class ModernMCPClient {
  private baseUrl: string;
  private sessionId?: string;
  private capabilities?: MCPCapabilities;
  private progressHandlers = new Map<string, (progress: ProgressNotification) => void>();
  private elicitationHandler?: (request: ElicitationRequest) => Promise<ElicitationResponse>;

  constructor(baseUrl: string = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }

  // Enhanced connection with backwards compatibility
  async connect(server: MCPServer): Promise<{
    sessionId: string;
    capabilities: MCPCapabilities;
    server: any;
  }> {
    const endpoint = `${this.baseUrl}/api/mcp-tools`;
    
    const requestBody = {
      path: server.path || server.stdioConfig?.command,
      args: server.stdioConfig?.args || [],
      env: server.stdioConfig?.env || {},
      transport: server.connectionType,
      url: server.url
    };

    try {
      // Try modern endpoint first
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'mcp-session-id': this.sessionId || ''
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      this.sessionId = result.sessionId;
      this.capabilities = result.capabilities;
      
      return result;

    } catch (error) {
      console.error('[MCP][MODERN] Connection failed:', error);
      
      // Fallback to legacy endpoint
      try {
        const legacyResponse = await fetch(`${this.baseUrl}/api/mcp-tools-legacy`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        });

        if (!legacyResponse.ok) {
          throw new Error(`Legacy connection failed: ${legacyResponse.status}`);
        }

        const legacyResult = await legacyResponse.json();
        console.log('[MCP][MODERN] Using legacy fallback');
        
        return {
          sessionId: 'legacy',
          capabilities: {},
          server: { tools: legacyResult.tools || [] }
        };

      } catch (legacyError) {
        console.error('[MCP][MODERN] Legacy fallback failed:', legacyError);
        throw error;
      }
    }
  }

  // Enhanced tool calling with progress support
  async callTool(
    toolName: string, 
    args: Record<string, any> = {},
    options: {
      progressToken?: string;
      onProgress?: (progress: ProgressNotification) => void;
    } = {}
  ): Promise<any> {
    if (!this.sessionId) {
      throw new Error('No active session. Call connect() first.');
    }

    const endpoint = `${this.baseUrl}/api/mcp-tools/${this.sessionId}/call`;
    
    const requestBody: any = {
      toolName,
      arguments: args
    };

    // Add progress token if provided
    if (options.progressToken) {
      requestBody.progressToken = options.progressToken;
      
      if (options.onProgress) {
        this.progressHandlers.set(options.progressToken, options.onProgress);
      }
    }

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'mcp-session-id': this.sessionId
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Tool call failed: ${response.status}`);
      }

      const result = await response.json();
      return result.result;

    } catch (error) {
      console.error('[MCP][MODERN] Tool call failed:', error);
      throw error;
    }
  }

  // Resource reading with enhanced error handling
  async readResource(uri: string): Promise<any> {
    if (!this.sessionId) {
      throw new Error('No active session. Call connect() first.');
    }

    const endpoint = `${this.baseUrl}/api/mcp-resources/${this.sessionId}/read`;
    
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'mcp-session-id': this.sessionId
        },
        body: JSON.stringify({ uri })
      });

      if (!response.ok) {
        throw new Error(`Resource read failed: ${response.status}`);
      }

      const result = await response.json();
      return result.result;

    } catch (error) {
      console.error('[MCP][MODERN] Resource read failed:', error);
      throw error;
    }
  }

  // Prompt execution
  async getPrompt(name: string, args: Record<string, any> = {}): Promise<any> {
    if (!this.sessionId) {
      throw new Error('No active session. Call connect() first.');
    }

    const endpoint = `${this.baseUrl}/api/mcp-prompts/${this.sessionId}/get`;
    
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'mcp-session-id': this.sessionId
        },
        body: JSON.stringify({ name, arguments: args })
      });

      if (!response.ok) {
        throw new Error(`Prompt execution failed: ${response.status}`);
      }

      const result = await response.json();
      return result.result;

    } catch (error) {
      console.error('[MCP][MODERN] Prompt execution failed:', error);
      throw error;
    }
  }

  // Session management
  async disconnect(): Promise<void> {
    if (!this.sessionId) {
      return;
    }

    const endpoint = `${this.baseUrl}/api/mcp-sessions/${this.sessionId}`;
    
    try {
      await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'mcp-session-id': this.sessionId
        }
      });

      this.sessionId = undefined;
      this.capabilities = undefined;
      this.progressHandlers.clear();

    } catch (error) {
      console.error('[MCP][MODERN] Disconnect failed:', error);
    }
  }

  // Progress notification handling
  handleProgressNotification(notification: ProgressNotification): void {
    const handler = this.progressHandlers.get(notification.progressToken.toString());
    if (handler) {
      handler(notification);
    }
  }

  // Elicitation support
  setElicitationHandler(handler: (request: ElicitationRequest) => Promise<ElicitationResponse>): void {
    this.elicitationHandler = handler;
  }

  async handleElicitationRequest(request: ElicitationRequest): Promise<ElicitationResponse> {
    if (!this.elicitationHandler) {
      return {
        action: 'reject'
      };
    }

    return await this.elicitationHandler(request);
  }

  // Health check
  async healthCheck(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`);
      return await response.json();
    } catch (error) {
      console.error('[MCP][MODERN] Health check failed:', error);
      throw error;
    }
  }

  // Getters
  get currentSessionId(): string | undefined {
    return this.sessionId;
  }

  get currentCapabilities(): MCPCapabilities | undefined {
    return this.capabilities;
  }

  get isConnected(): boolean {
    return !!this.sessionId;
  }
}

// Singleton instance for global use
export const modernMCPClient = new ModernMCPClient();
