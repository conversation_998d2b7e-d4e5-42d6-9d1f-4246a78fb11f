
import { MCPServer, ServerConnection, LLMModel, MCPTool } from "@/types/mcp";
import { toast } from "sonner";

// SSE Connection management
export const connectSSE = async (
  server: MCPServer,
  onEvent: (event: MessageEvent) => void,
  onError: (error: Event) => void,
  onOpen?: () => void
): Promise<EventSource> => {
  try {
    if (!server.url) {
      throw new Error("Server URL is missing for SSE connection");
    }
    
    console.log(`Connecting to SSE server: ${server.url}`);
    
    // Create EventSource for SSE connection
    const eventSource = new EventSource(server.url);
    
    // Add event listeners
    eventSource.onmessage = onEvent;
    eventSource.onerror = onError;
    eventSource.onopen = onOpen ?? (() => {
      console.log(`SSE connection opened to ${server.name}`);
    });
    
    return eventSource;
  } catch (error) {
    console.error(`Error connecting to SSE server ${server.name}:`, error);
    toast.error(`Failed to connect to server ${server.name}`);
    throw error;
  }
};

// STDIO Connection (placeholder - actual implementation would interact with a backend)
export const connectSTDIO = async (
  server: MCPServer
): Promise<boolean> => {
  try {
    if (!server.path || !server.stdioConfig) {
      throw new Error("STDIO configuration is incomplete");
    }
    
    console.log(`Starting STDIO connection to ${server.path} with command: ${server.stdioConfig.command}`);
    
    // In a real implementation, this would start a process using server.path
    return true;
  } catch (error) {
    console.error(`Error starting STDIO connection for ${server.name}:`, error);
    toast.error(`Failed to start STDIO connection for ${server.name}`);
    return false;
  }
};

// Get models from a server - ONLY REAL DATA
export const fetchModelsFromServer = async (server: MCPServer): Promise<LLMModel[]> => {
  try {
    if (!server.isActive) {
      throw new Error("Server is not active");
    }
    
    console.log(`Attempting to fetch models from server: ${server.name}`);
    
    if (server.connectionType === 'sse') {
      try {
        if (!server.url) {
          throw new Error("Server URL is missing for SSE connection");
        }
        
        console.log(`Fetching models from ${server.url}/v1/models`);
        
        // Make an actual request to the server
        const response = await fetch(`${server.url}/v1/models`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch models: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log("Models response:", data);
        
        // No models returned
        if (!data.data || data.data.length === 0) {
          toast.warning(`No models available from server ${server.name}`);
          return [];
        }
        
        // Map the response to our LLMModel type
        return data.data.map((model: any) => ({
          id: model.id || `${server.id}-${model.name || 'unknown'}`,
          name: model.name || model.id || 'Unknown Model',
          provider: model.provider || 'unknown',
          isAvailable: true,
          modelId: model.id || model.name,
          contextLength: model.context_length,
          capabilities: {
            vision: model.capabilities?.vision || false,
            audio: model.capabilities?.audio || false,
            streaming: model.capabilities?.streaming || true,
            function_calling: model.capabilities?.function_calling || false
          }
        }));
      } catch (error) {
        console.error(`Error fetching models from ${server.name}:`, error);
        toast.error(`Failed to fetch models from ${server.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return [];
      }
    } else if (server.connectionType === 'stdio') {
      try {
        console.log(`Fetching models from STDIO server ${server.name}`);
        // In real implementation, this would communicate with the STDIO process
        toast.warning(`STDIO model fetching not fully implemented for ${server.name}`);
        return [];
      } catch (error) {
        console.error(`Error fetching models from STDIO server ${server.name}:`, error);
        toast.error(`Failed to fetch models from ${server.name}`);
        return [];
      }
    }
    
    return [];
  } catch (error) {
    console.error(`Error fetching models from ${server.name}:`, error);
    toast.error(`Failed to fetch models from ${server.name}`);
    return [];
  }
};

// Get tools from a server - ONLY REAL DATA
export const fetchToolsFromServer = async (server: MCPServer): Promise<MCPTool[]> => {
  try {
    if (!server.isActive) {
      throw new Error("Server is not active");
    }
    
    console.log(`Attempting to fetch tools from server: ${server.name}`);
    
    if (server.connectionType === 'sse' && server.url) {
      // Make real request to fetch tools
      try {
        console.log(`Fetching tools from ${server.url}/v1/tools`);
        const response = await fetch(`${server.url}/v1/tools`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch tools: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log("Tools response:", data);
        
        if (!data.tools || data.tools.length === 0) {
          console.warn(`No tools available from server ${server.name}`);
          return [];
        }
        
        // Map tools to our format
        return data.tools.map((tool: any) => ({
          id: tool.id || crypto.randomUUID(),
          name: tool.name,
          description: tool.description || '',
          serverId: server.id,
          parameters: tool.parameters || []
        }));
      } catch (error) {
        console.error(`Error fetching tools from ${server.name}:`, error);
        toast.error(`Failed to fetch tools from ${server.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return [];
      }
    } else if (server.connectionType === 'stdio') {
      try {
        console.log(`[STDIO][API] Solicitando herramientas a backend para ${server.name}`);
        if (!server.path || !server.stdioConfig) {
          console.error(`[STDIO][API] Configuración incompleta: path=${server.path}, stdioConfig=${JSON.stringify(server.stdioConfig)}`);
          throw new Error("STDIO configuration is incomplete");
        }
        // Llamada HTTP al backend
        const response = await fetch('/api/mcp-tools', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            path: server.path,
            args: server.stdioConfig?.args || [],
            env: server.stdioConfig?.env || {}
          })
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Unknown error from backend');
        }
        const toolsJson = await response.json();
        if (!toolsJson.tools || !Array.isArray(toolsJson.tools)) {
          throw new Error('Backend did not return a "tools" array');
        }
        const mapped = toolsJson.tools.map((tool: any) => ({
          id: tool.id || crypto.randomUUID(),
          name: tool.name,
          description: tool.description || '',
          serverId: server.id,
          parameters: tool.parameters || []
        }));
        console.log(`[STDIO][API] Herramientas recibidas/mapeadas para ${server.name}:`, mapped);
        return mapped;
      } catch (error) {
        console.error(`[STDIO][API] Error general al obtener herramientas de ${server.name}:`, error);
        toast.error(`Failed to fetch tools from ${server.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return [];
      }
    }
    
    return [];
  } catch (error) {
    console.error(`Error fetching tools from ${server.name}:`, error);
    toast.error(`Failed to fetch tools from ${server.name}`);
    return [];
  }
};

// Send a message to the server
export const sendMessageToServer = async (
  server: MCPServer,
  modelId: string,
  provider: string,
  apiKey: string | undefined,
  messages: any[],
  onResponseChunk?: (chunk: string) => void,
  onError?: (error: any) => void,
  onComplete?: () => void
): Promise<string> => {
  try {
    if (!server.isActive) {
      throw new Error("Server is not active");
    }
    
    console.log(`Sending message to server: ${server.name}, model: ${modelId}, provider: ${provider}`);
    
    if (server.connectionType === 'sse') {
      if (!server.url) {
        throw new Error("Server URL is missing for SSE connection");
      }
      
      // Prepare the request payload
      const payload = {
        model: modelId,
        messages: messages,
        stream: true,
        provider: provider
      };
      
      // Add API key if provided
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      if (apiKey) {
        headers['Authorization'] = `Bearer ${apiKey}`;
      }
      
      console.log(`Sending request to ${server.url}/v1/chat/completions`, { payload, headers });
      
      // For a real SSE server, we would POST the message and then listen for events
      const response = await fetch(`${server.url}/v1/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Failed to send message: ${response.status} ${response.statusText}. ${errorText}`);
      }
      
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      
      if (reader) {
        let done = false;
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          
          if (value) {
            const chunk = decoder.decode(value);
            console.log("Received chunk:", chunk);
            fullResponse += chunk;
            onResponseChunk?.(chunk);
          }
        }
      }
      
      onComplete?.();
      return fullResponse;
    } else if (server.connectionType === 'stdio') {
      // For STDIO connections
      if (!server.stdioConfig || !server.path) {
        throw new Error(`Invalid STDIO configuration for ${server.name}`);
      }
      
      try {
        toast.warning(`STDIO message sending not fully implemented for ${server.name}`);
        const error = new Error("STDIO connection not fully implemented");
        onError?.(error);
        return error.message;
      } catch (error) {
        console.error(`Error sending message to STDIO server ${server.name}:`, error);
        onError?.(error);
        return error instanceof Error ? error.message : "Unknown error";
      }
    }
    
    return "Unknown connection type";
  } catch (error) {
    console.error(`Error sending message to ${server.name}:`, error);
    onError?.(error);
    toast.error(`Failed to send message to ${server.name}: ${error instanceof Error ? error.message : "Unknown error"}`);
    return error instanceof Error ? error.message : "Unknown error";
  }
};
