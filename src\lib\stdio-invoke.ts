import { spawn } from "child_process";

/**
 * Invoca un comando vía stdio y retorna la salida como string.
 * @param path Ruta o comando a ejecutar (ej: 'npx')
 * @param args Argumentos para el comando
 * @param input JSON serializado a enviar por stdin (opcional)
 * @returns Promesa con el resultado (stdout)
 */
export async function invokeViaStdio({
  path,
  args = [],
  input
}: {
  path: string;
  args?: string[];
  input?: any;
}): Promise<string> {
  return new Promise((resolve, reject) => {
    const proc = spawn(path, args, { stdio: ["pipe", "pipe", "pipe"] });
    let stdout = "";
    let stderr = "";

    proc.stdout.on("data", (data) => {
      stdout += data.toString();
    });
    proc.stderr.on("data", (data) => {
      stderr += data.toString();
    });
    proc.on("error", (err) => {
      reject(err);
    });
    proc.on("close", (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new Error(`STDIO process exited with code ${code}: ${stderr}`));
      }
    });
    if (input !== undefined) {
      proc.stdin.write(typeof input === "string" ? input : JSON.stringify(input));
    }
    proc.stdin.end();
  });
}
