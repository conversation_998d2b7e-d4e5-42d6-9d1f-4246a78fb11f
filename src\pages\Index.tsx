
import { useEffect, useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { ChatInterface } from "@/components/ChatInterface";
import { ServerConfigDialog } from "@/components/ServerConfigDialog";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import { ParticleBackground, WaveBackground } from "@/components/BackgroundEffects";
import { Toaster } from "sonner";
import { ServerStats } from "@/components/ServerStats";
import { useMCP } from "@/context/MCPContext";

export default function Index() {
  const [isServerDialogOpen, setIsServerDialogOpen] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(true);
  const { servers, activeServers } = useMCP();

  useEffect(() => {
    // Check if this is the first time the user is visiting the app
    const hasVisited = localStorage.getItem('mcp-has-visited');
    
    // Also consider having servers as "not first time"
    if (!hasVisited && servers.length === 0) {
      setIsFirstTime(true);
    } else {
      setIsFirstTime(false);
      if (!hasVisited) {
        localStorage.setItem('mcp-has-visited', 'true');
      }
    }
  }, [servers]);

  return (
    <div className="flex h-screen bg-background text-foreground">
      <ParticleBackground />
      <WaveBackground />
      <Toaster position="top-right" />
      
      <Sidebar />
      <main className="flex-1 flex flex-col overflow-hidden relative">
        {isFirstTime ? (
          <div className="flex flex-col items-center justify-center h-full p-4 space-y-6 z-10">
            <div className="max-w-2xl text-center glass p-8 rounded-xl slide-in-up">
              <img 
                src="/logo.png" 
                alt="Grupo SAI Logo" 
                className="h-24 mx-auto mb-6 float"
              />
              <h1 className="text-3xl font-bold mb-4 text-primary">Bienvenido al Cliente MCP</h1>
              <p className="mb-6 text-muted-foreground">
                Conecta a múltiples servidores MCP simultáneamente, elige tus modelos LLM favoritos,
                y utiliza potentes herramientas con esta interfaz de chat intuitiva.
              </p>
              <div className="mb-6">
                <ServerStats />
              </div>
              <Button 
                size="lg" 
                onClick={() => setIsServerDialogOpen(true)}
                className="flex items-center gap-2 hover-scale gradient-btn text-primary-foreground"
              >
                <PlusIcon size={16} />
                Añadir Primer Servidor MCP
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            <div className="p-2 border-b">
              <ServerStats />
            </div>
            <div className="flex-1">
              <ChatInterface />
            </div>
          </div>
        )}
      </main>
      
      <ServerConfigDialog 
        isOpen={isServerDialogOpen} 
        onClose={() => setIsServerDialogOpen(false)} 
      />
    </div>
  );
}
