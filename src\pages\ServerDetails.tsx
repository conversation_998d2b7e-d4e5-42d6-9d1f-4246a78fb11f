
import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useMCP } from "@/context/MCPContext";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  CloudOff,
  Database,
  Edit,
  Eye,
  EyeOff,
  Save,
  Server,
  Trash,
} from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { MCPServer } from "@/types/mcp";
import { ServerConfigDialog } from "@/components/ServerConfigDialog";

export default function ServerDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { 
    servers, 
    updateServer, 
    removeServer, 
    providers, 
    updateProviderConfig, 
    connectToServer,
    disconnectFromServer,
    connections,
    fetchModels
  } = useMCP();
  
  const [server, setServer] = useState<MCPServer | null>(null);
  const [activeTab, setActiveTab] = useState("general");
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  
  const connectionStatus = connections.find(conn => conn.serverId === id)?.status || 'disconnected';
  
  useEffect(() => {
    if (id) {
      const foundServer = servers.find(s => s.id === id);
      if (foundServer) {
        setServer({ ...foundServer });
      } else {
        toast.error("Server not found");
        navigate("/");
      }
    }
  }, [id, servers, navigate]);
  
  const handleUpdateServer = () => {
    if (server) {
      updateServer(server);
      toast.success("Server updated successfully");
    }
  };
  
  const handleDeleteServer = () => {
    if (server) {
      try {
        removeServer(server.id);
        toast.success("Server deleted successfully");
        navigate("/");
      } catch (error) {
        console.error("Error deleting server:", error);
        toast.error(`Failed to delete server: ${error}`);
      }
      setShowDeleteConfirm(false);
    }
  };
  
  const handleConnect = async () => {
    if (server) {
      setLoading(true);
      try {
        await connectToServer(server.id);
        toast.success(`Connected to ${server.name}`);
        await fetchModels(server.id);
      } catch (error) {
        toast.error(`Failed to connect to ${server.name}`);
        console.error("Connection error:", error);
      } finally {
        setLoading(false);
      }
    }
  };
  
  const handleDisconnect = () => {
    if (server) {
      disconnectFromServer(server.id);
      toast.success(`Disconnected from ${server.name}`);
    }
  };
  
  const handleProviderChange = (providerId: string, field: string, value: any) => {
    updateProviderConfig(providerId, { [field]: value });
  };
  
  const toggleSecretVisibility = (providerId: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [providerId]: !prev[providerId]
    }));
  };

  const updateStdioEnv = (envText: string) => {
    if (server) {
      const envVars = envText
        .split('\n')
        .filter(Boolean)
        .reduce((acc, line) => {
          const [key, ...valueParts] = line.split('=');
          if (key && valueParts.length) {
            acc[key.trim()] = valueParts.join('=').trim();
          }
          return acc;
        }, {} as Record<string, string>);
      
      setServer({ 
        ...server, 
        stdioConfig: {
          ...server.stdioConfig as any,
          env: envVars
        }
      });
    }
  };
  
  if (!server) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p>Loading server details...</p>
      </div>
    );
  }
  
  return (
    <div className="container py-8 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate("/")}
          className="flex items-center gap-2"
        >
          <ArrowLeft size={16} />
          Back to Dashboard
        </Button>
        
        <div className="flex items-center gap-2">
          {connectionStatus === 'connected' ? (
            <Badge className="bg-green-500">Connected</Badge>
          ) : connectionStatus === 'connecting' ? (
            <Badge className="bg-yellow-500">Connecting</Badge>
          ) : connectionStatus === 'error' ? (
            <Badge className="bg-red-500">Error</Badge>
          ) : (
            <Badge className="bg-gray-500">Disconnected</Badge>
          )}
          
          {connectionStatus === 'connected' ? (
            <Button 
              variant="outline" 
              onClick={handleDisconnect}
              className="flex items-center gap-2"
            >
              <CloudOff size={16} />
              Disconnect
            </Button>
          ) : (
            <Button 
              variant="default" 
              onClick={handleConnect}
              className="flex items-center gap-2 hover-scale gradient-btn text-primary-foreground"
              disabled={connectionStatus === 'connecting' || loading}
            >
              <Server size={16} />
              {loading ? "Connecting..." : "Connect"}
            </Button>
          )}
        </div>
      </div>
      
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold text-primary">{server.name}</h1>
        <div className="flex gap-2">
          <Badge>{server.connectionType === 'sse' ? 'Server Sent Events' : 'Standard I/O'}</Badge>
          <Button 
            variant="outline" 
            size="icon"
            onClick={() => setShowEditModal(true)}
            className="flex items-center justify-center"
            title="Edit Server"
          >
            <Edit size={16} />
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="connection">Connection</TabsTrigger>
          <TabsTrigger value="providers">Model Providers</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Server Information</CardTitle>
              <CardDescription>Basic information about this MCP server.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Server Name</Label>
                <Input
                  id="name"
                  value={server.name}
                  onChange={(e) => setServer({ ...server, name: e.target.value })}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="connection-type">Connection Type</Label>
                <Select
                  value={server.connectionType}
                  onValueChange={(value: 'sse' | 'stdio') => 
                    setServer({ ...server, connectionType: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select connection type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sse">Server Sent Events (SSE)</SelectItem>
                    <SelectItem value="stdio">Standard I/O (STDIO)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                variant="destructive" 
                onClick={() => setShowDeleteConfirm(true)}
                className="flex items-center gap-2"
              >
                <Trash size={16} />
                Delete Server
              </Button>
              <Button 
                onClick={handleUpdateServer}
                className="flex items-center gap-2"
              >
                <Save size={16} />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="connection" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Connection Settings</CardTitle>
              <CardDescription>Configure how to connect to this server.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {server.connectionType === 'sse' ? (
                <div className="space-y-2">
                  <Label htmlFor="url">Server URL</Label>
                  <Input
                    id="url"
                    value={server.url || ''}
                    onChange={(e) => setServer({ ...server, url: e.target.value })}
                    placeholder="https://your-mcp-server.com/v1"
                  />
                  <p className="text-sm text-muted-foreground">
                    The base URL for your MCP server, e.g. https://your-mcp-server.com/v1
                  </p>
                </div>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="path">Executable Path</Label>
                    <Input
                      id="path"
                      value={server.path || ''}
                      onChange={(e) => setServer({ ...server, path: e.target.value })}
                      placeholder="/path/to/mcp-binary"
                    />
                    <p className="text-sm text-muted-foreground">
                      Full path to the MCP executable on your system
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="command">Command</Label>
                    <Input
                      id="command"
                      value={server.stdioConfig?.command || ''}
                      onChange={(e) => 
                        setServer({ 
                          ...server, 
                          stdioConfig: {
                            ...server.stdioConfig as any,
                            command: e.target.value
                          }
                        })
                      }
                      placeholder="python -m mcp_server"
                    />
                    <p className="text-sm text-muted-foreground">
                      The command to execute (if different from the path)
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="args">Command Arguments</Label>
                    <Input
                      id="args"
                      value={server.stdioConfig?.args?.join(' ') || ''}
                      onChange={(e) => 
                        setServer({ 
                          ...server, 
                          stdioConfig: {
                            ...server.stdioConfig as any,
                            args: e.target.value.split(' ').filter(Boolean)
                          }
                        })
                      }
                      placeholder="--config /path/to/config.json"
                    />
                    <p className="text-sm text-muted-foreground">
                      Arguments to pass to the command, space-separated
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="env">Environment Variables</Label>
                    <Textarea
                      id="env"
                      value={
                        server.stdioConfig?.env 
                          ? Object.entries(server.stdioConfig.env)
                              .map(([key, value]) => `${key}=${value}`)
                              .join('\n')
                          : ''
                      }
                      onChange={(e) => updateStdioEnv(e.target.value)}
                      placeholder="VAR_NAME=value"
                      className="min-h-[120px]"
                    />
                    <p className="text-sm text-muted-foreground">
                      Environment variables to set when running the command (one per line, format: KEY=value)
                    </p>
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleUpdateServer}
                className="w-full flex items-center gap-2"
              >
                <Save size={16} />
                Save Connection Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="providers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Model Providers</CardTitle>
              <CardDescription>Configure API keys and endpoints for model providers.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {providers
                .filter(provider => provider.type !== 'local')
                .map(provider => (
                <div key={provider.id} className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold">{provider.name}</h3>
                    <Badge>{provider.type}</Badge>
                  </div>
                  
                  {provider.requiresApiKey && (
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor={`${provider.id}-api-key`}>API Key</Label>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => toggleSecretVisibility(provider.id)}
                          className="h-6 w-6 p-0"
                        >
                          {showSecrets[provider.id] ? <EyeOff size={14} /> : <Eye size={14} />}
                        </Button>
                      </div>
                      <Input
                        id={`${provider.id}-api-key`}
                        type={showSecrets[provider.id] ? "text" : "password"}
                        value={provider.apiKey || ''}
                        onChange={(e) => handleProviderChange(provider.id, 'apiKey', e.target.value)}
                        placeholder={`Enter your ${provider.name} API key`}
                      />
                      <p className="text-xs text-muted-foreground">
                        Your API key is stored securely in local storage and is never sent to our servers.
                      </p>
                    </div>
                  )}
                  
                  {provider.requiresEndpoint && (
                    <div className="space-y-2">
                      <Label htmlFor={`${provider.id}-endpoint`}>Endpoint</Label>
                      <Input
                        id={`${provider.id}-endpoint`}
                        value={provider.endpoint || ''}
                        onChange={(e) => handleProviderChange(provider.id, 'endpoint', e.target.value)}
                        placeholder={`Enter custom ${provider.name} endpoint`}
                      />
                    </div>
                  )}
                  
                  <Separator />
                </div>
              ))}
            </CardContent>
            <CardFooter className="justify-end">
              <Button className="flex items-center gap-2">
                <Database size={16} />
                Save Provider Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Confirm Delete Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Server</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the server "{server.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteServer}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Server Dialog */}
      <ServerConfigDialog 
        isOpen={showEditModal} 
        onClose={() => setShowEditModal(false)} 
        serverToEdit={server}
      />
    </div>
  );
}
