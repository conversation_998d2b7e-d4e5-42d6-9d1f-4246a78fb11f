
/* Typing indicator animation */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator .dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: hsl(var(--muted-foreground));
  margin: 0 2px;
  animation: pulse 1.5s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 50%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  25% {
    transform: scale(1.5);
    opacity: 1;
  }
}

/* Code block styling */
pre {
  background-color: hsl(var(--muted));
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  font-family: monospace;
  font-size: 0.875rem;
  margin: 1rem 0;
}

code {
  font-family: monospace;
  font-size: 0.875rem;
  background-color: hsl(var(--muted));
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
}
