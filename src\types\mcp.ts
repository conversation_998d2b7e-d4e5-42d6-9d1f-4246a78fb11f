
export interface MCPServer {
  id: string;
  name: string;
  url: string;
  connectionType: 'sse' | 'stdio';
  isActive: boolean;
  path?: string; // For STDIO connections
  stdioConfig?: {
    command: string;
    args?: string[];
    env?: Record<string, string>;
  };
}

export interface LLMProvider {
  id: string;
  name: string;
  type: 'openai' | 'gemini' | 'local' | 'anthropic' | 'cohere' | 'mistral' | 'ollama' | 'llama' | 'other';
  requiresApiKey: boolean;
  requiresEndpoint: boolean;
  apiKey?: string;
  endpoint?: string;
  models: string[]; // List of model IDs supported by this provider
}

export interface LLMModel {
  id: string;
  name: string;
  provider: string; // Reference to provider id
  isAvailable: boolean;
  modelId: string; // The actual model identifier (e.g., "gpt-4o")
  contextLength?: number; // Maximum context length
  capabilities?: {
    vision?: boolean;
    audio?: boolean;
    streaming?: boolean;
    function_calling?: boolean;
  };
  maxTokens?: number;
  config?: Record<string, any>; // Provider-specific settings
  supportedServerIds?: string[]; // List of server IDs that can use this model
}

export interface MCPTool {
  id: string;
  name: string;
  description: string;
  serverId: string;
  parameters: ToolParameter[];
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  default?: any;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: number;
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
}

export interface ToolCall {
  id: string;
  toolId: string;
  params: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'error';
}

export interface ToolResult {
  toolCallId: string;
  result: any;
  error?: string;
}

export interface Conversation {
  id: string;
  title: string;
  modelId: string;
  selectedServerId: string; // This is no longer used actively but kept for backward compatibility
  messages: ChatMessage[];
  createdAt: number;
  updatedAt: number;
}

export interface ServerConnection {
  serverId: string;
  status: 'connected' | 'disconnected' | 'error' | 'connecting';
  lastConnected?: number;
  error?: string;
  stream?: EventSource | null; // For SSE connections
  process?: any; // For STDIO connections
}

// Provider-specific configuration types
export interface OpenAIConfig {
  apiKey: string;
  organization?: string;
  endpoint?: string; // For Azure or custom endpoints
}

export interface AnthropicConfig {
  apiKey: string;
  endpoint?: string;
}

export interface GeminiConfig {
  apiKey: string;
  endpoint?: string;
}

export interface MistralConfig {
  apiKey: string;
  endpoint?: string;
}

export interface OllamaConfig {
  endpoint: string; // Local endpoint, e.g., http://localhost:11434
}

export interface CohereConfig {
  apiKey: string;
  endpoint?: string;
}

export interface ProviderConfigMap {
  openai: OpenAIConfig;
  anthropic: AnthropicConfig;
  gemini: GeminiConfig;
  mistral: MistralConfig;
  ollama: OllamaConfig;
  cohere: CohereConfig;
  [key: string]: any;
}
