
import { MCPServer, LL<PERSON>odel, ChatMessage } from "@/types/mcp";

export interface ConnectionHandler {
  connect: () => Promise<boolean>;
  disconnect: () => void;
  isConnected: () => boolean;
  sendMessage: (
    message: string, 
    model: LLMModel, 
    onChunk: (chunk: string) => void,
    onToolCall: (toolId: string, params: Record<string, any>) => void,
    onComplete: (finalMessage: ChatMessage) => void,
    onError: (error: Error) => void
  ) => Promise<void>;
}

export class SSEConnectionHandler implements ConnectionHandler {
  private server: MCPServer;
  private eventSource: EventSource | null = null;

  constructor(server: MCPServer) {
    this.server = server;
  }

  async connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        // Close any existing connection
        this.disconnect();

        // Connect to the server
        this.eventSource = new EventSource(`${this.server.url}/stream`);

        this.eventSource.onopen = () => {
          console.log(`Connected to SSE server: ${this.server.url}`);
          resolve(true);
        };

        this.eventSource.onerror = (error) => {
          console.error(`Error connecting to SSE server: ${this.server.url}`, error);
          this.disconnect();
          reject(new Error(`Failed to connect to SSE server: ${error}`));
        };
      } catch (error) {
        console.error(`Error setting up SSE connection: ${error}`);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      console.log(`Disconnected from SSE server: ${this.server.url}`);
    }
  }

  isConnected(): boolean {
    return this.eventSource !== null && this.eventSource.readyState === EventSource.OPEN;
  }

  async sendMessage(
    message: string,
    model: LLMModel,
    onChunk: (chunk: string) => void,
    onToolCall: (toolId: string, params: Record<string, any>) => void,
    onComplete: (finalMessage: ChatMessage) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    if (!this.isConnected()) {
      try {
        await this.connect();
      } catch (error) {
        onError(new Error(`Failed to connect to server: ${error}`));
        return;
      }
    }

    try {
      // Set up event listeners for the response
      if (this.eventSource) {
        // For content chunks
        this.eventSource.addEventListener('content', (event) => {
          const data = JSON.parse(event.data);
          onChunk(data.content);
        });

        // For tool calls
        this.eventSource.addEventListener('tool_call', (event) => {
          const data = JSON.parse(event.data);
          onToolCall(data.toolId, data.params);
        });

        // For completion
        this.eventSource.addEventListener('complete', (event) => {
          const finalMessage = JSON.parse(event.data);
          onComplete(finalMessage);

          // Remove the event listeners
          this.eventSource?.removeEventListener('content', () => {});
          this.eventSource?.removeEventListener('tool_call', () => {});
          this.eventSource?.removeEventListener('complete', () => {});
        });

        // Send the message to the server
        fetch(`${this.server.url}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            message,
            model: model.modelId
          })
        });
      }
    } catch (error) {
      onError(new Error(`Error sending message: ${error}`));
    }
  }
}

export class STDIOConnectionHandler implements ConnectionHandler {
  private server: MCPServer;
  private connected: boolean = false;
  private processId: string | null = null;

  constructor(server: MCPServer) {
    this.server = server;
  }

  async connect(): Promise<boolean> {
    try {
      // For STDIO, we'll simulate a connection to a local process
      // In a real implementation, this would start a child process or connect to one
      console.log(`Connecting to STDIO process at ${this.server.path}`);

      // Simulate starting a process
      this.processId = crypto.randomUUID();
      this.connected = true;
      
      return true;
    } catch (error) {
      console.error(`Error connecting to STDIO process: ${error}`);
      this.connected = false;
      throw error;
    }
  }

  disconnect(): void {
    if (this.connected) {
      console.log(`Disconnecting from STDIO process: ${this.processId}`);
      // In a real implementation, this would terminate the child process
      this.processId = null;
      this.connected = false;
    }
  }

  isConnected(): boolean {
    return this.connected;
  }

  async sendMessage(
    message: string,
    model: LLMModel,
    onChunk: (chunk: string) => void,
    onToolCall: (toolId: string, params: Record<string, any>) => void,
    onComplete: (finalMessage: ChatMessage) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    if (!this.isConnected()) {
      try {
        await this.connect();
      } catch (error) {
        onError(new Error(`Failed to connect to STDIO process: ${error}`));
        return;
      }
    }

    try {
      console.log(`Sending message to STDIO process: ${this.processId}`);
      console.log(`Message: ${message}`);
      console.log(`Model: ${model.modelId}`);

      // Simulate the response streaming
      let fullContent = "";
      const intervalId = setInterval(() => {
        const chunk = "This is a simulated chunk of the response. ";
        fullContent += chunk;
        onChunk(chunk);
      }, 300);

      // Simulate a tool call after a delay
      setTimeout(() => {
        onToolCall("calculator", { expression: "2 + 2" });
      }, 2000);

      // Simulate completion after a few seconds
      setTimeout(() => {
        clearInterval(intervalId);
        
        const finalMessage: ChatMessage = {
          id: crypto.randomUUID(),
          role: 'assistant',
          content: fullContent + "This is the end of the simulated response.",
          timestamp: Date.now(),
          toolCalls: [
            {
              id: crypto.randomUUID(),
              toolId: "calculator",
              params: { expression: "2 + 2" },
              status: 'completed'
            }
          ],
          toolResults: [
            {
              toolCallId: "calculator",
              result: "4"
            }
          ]
        };
        
        onComplete(finalMessage);
      }, 5000);
    } catch (error) {
      onError(new Error(`Error sending message to STDIO process: ${error}`));
    }
  }
}

export const createConnectionHandler = (server: MCPServer): ConnectionHandler => {
  if (server.connectionType === 'sse') {
    return new SSEConnectionHandler(server);
  } else if (server.connectionType === 'stdio') {
    return new STDIOConnectionHandler(server);
  } else {
    throw new Error(`Unsupported connection type: ${server.connectionType}`);
  }
};
