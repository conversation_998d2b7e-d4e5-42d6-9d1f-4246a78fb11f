
import { supabase } from "@/integrations/supabase/client";
import { MCPServer } from "@/types/mcp";

// Function to load servers from Supabase
export const loadServersFromSupabase = async (): Promise<MCPServer[]> => {
  try {
    const { data, error } = await supabase
      .from('chat_aaa_servers')
      .select('*');
    
    if (error) {
      console.error("Error loading servers:", error);
      throw error;
    }

    // Map the Supabase data to our MCPServer type
    return (data || []).map(server => ({
      id: server.id,
      name: server.name,
      connectionType: server.connection_type as 'sse' | 'stdio',
      url: server.url || undefined,
      path: server.path || undefined,
      stdioConfig: server.stdio_config ? {
        command: typeof server.stdio_config === 'object' && 'command' in server.stdio_config 
          ? String(server.stdio_config.command) 
          : '',
        args: typeof server.stdio_config === 'object' && 'args' in server.stdio_config && Array.isArray(server.stdio_config.args) 
          ? server.stdio_config.args.map(arg => String(arg)) 
          : [],
        env: typeof server.stdio_config === 'object' && 'env' in server.stdio_config && typeof server.stdio_config.env === 'object' 
          ? server.stdio_config.env as Record<string, string>
          : {}
      } : undefined,
      isActive: server.is_active
    }));
  } catch (error) {
    console.error("Error in loadServersFromSupabase:", error);
    return [];
  }
};

// Function to save a server to Supabase
export const saveServerToSupabase = async (server: MCPServer): Promise<boolean> => {
  try {
    console.log("Saving server to Supabase:", server);
    
    // Convert MCP server to Supabase format
    const serverData = {
      id: server.id,
      name: server.name,
      connection_type: server.connectionType,
      url: server.url || null,
      path: server.path || null,
      stdio_config: server.stdioConfig ? {
        command: server.stdioConfig.command || '',
        args: server.stdioConfig.args || [],
        env: server.stdioConfig.env || {}
      } : null,
      is_active: server.isActive
    };

    // Check if the server exists
    const { data: existingServer } = await supabase
      .from('chat_aaa_servers')
      .select('id')
      .eq('id', server.id)
      .single();

    let result;
    if (existingServer) {
      console.log("Updating existing server:", server.id);
      // Update existing server
      result = await supabase
        .from('chat_aaa_servers')
        .update(serverData)
        .eq('id', server.id);
    } else {
      console.log("Inserting new server");
      // Insert new server
      result = await supabase
        .from('chat_aaa_servers')
        .insert(serverData);
    }

    if (result.error) {
      console.error("Error saving server:", result.error);
      return false;
    }
    console.log("Server saved successfully:", server.id);
    return true;
  } catch (error) {
    console.error("Error in saveServerToSupabase:", error);
    return false;
  }
};

// Function to delete a server from Supabase
export const deleteServerFromSupabase = async (serverId: string): Promise<boolean> => {
  try {
    console.log("Deleting server from Supabase:", serverId);
    const { error } = await supabase
      .from('chat_aaa_servers')
      .delete()
      .eq('id', serverId);
    
    if (error) {
      console.error("Error deleting server:", error);
      return false;
    }
    console.log("Server deleted successfully:", serverId);
    return true;
  } catch (error) {
    console.error("Error in deleteServerFromSupabase:", error);
    return false;
  }
};

// Function to update server active status
export const updateServerActiveStatus = async (serverId: string, isActive: boolean): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('chat_aaa_servers')
      .update({ is_active: isActive })
      .eq('id', serverId);
    
    if (error) {
      console.error("Error updating server active status:", error);
      return false;
    }
    return true;
  } catch (error) {
    console.error("Error in updateServerActiveStatus:", error);
    return false;
  }
};
