# 🧪 Prueba de Configuración MCP Firebird

## 📋 **Configuraciones de Prueba**

### **1. Configuración SSE (Recomendada para Firebird)**
```json
{
  "name": "Firebird Local SSE",
  "connectionType": "sse",
  "url": "http://localhost:3050/sse"
}
```

### **2. Configuración STDIO (Para MCP Firebird local)**
```json
{
  "name": "Firebird Local STDIO",
  "connectionType": "stdio",
  "path": "npx",
  "args": "mcp-firebird --database C:\\path\\to\\database.fdb --user SYSDBA --password masterkey",
  "env": {
    "FIREBIRD_HOST": "localhost",
    "FIREBIRD_PORT": "3050",
    "FIREBIRD_USER": "SYSDBA",
    "FIREBIRD_PASSWORD": "masterkey"
  }
}
```

### **3. Configuración WebSocket (Experimental)**
```json
{
  "name": "Firebird WebSocket",
  "connectionType": "websocket",
  "url": "ws://localhost:3050/mcp"
}
```

## 🔧 **Pasos para Probar**

### **Paso 1: Verificar MCP Firebird**
```bash
# Verificar que MCP Firebird esté instalado
npx mcp-firebird --help

# Si no está instalado:
npm install -g mcp-firebird
```

### **Paso 2: Configurar en ChatMCP**
1. Abrir ChatMCP: http://localhost:5173
2. Hacer clic en "+" para agregar servidor
3. Usar una de las configuraciones de arriba
4. Hacer clic en "Add Server"
5. Verificar que aparezca como "Conectado"

### **Paso 3: Probar Conexión**
1. Ir a la página del servidor
2. Verificar que aparezcan las herramientas disponibles:
   - `execute-query`
   - `list-tables`
   - `describe-table`
   - `get-field-descriptions`
   - `analyze-query-performance`
   - `backup-database`
   - `validate-database`

## 🐛 **Solución de Problemas**

### **Error: "Server URL is required"**
- **Causa**: Falta la URL para conexiones SSE/WebSocket
- **Solución**: Asegúrate de llenar el campo URL

### **Error: "Executable path is required"**
- **Causa**: Falta el path para conexiones STDIO
- **Solución**: Asegúrate de llenar el campo Path

### **Error: "Connection failed"**
- **Causa**: El servidor MCP Firebird no está ejecutándose
- **Solución**: 
  ```bash
  # Para STDIO, verificar que el comando funcione:
  npx mcp-firebird --database C:\\path\\to\\database.fdb --user SYSDBA --password masterkey
  
  # Para SSE, verificar que el servidor esté en el puerto correcto
  curl http://localhost:3050/sse
  ```

### **Error: "Failed to add server"**
- **Causa**: Error en la validación o guardado
- **Solución**: 
  1. Verificar que todos los campos requeridos estén llenos
  2. Revisar la consola del navegador para errores
  3. Verificar que el backend esté funcionando: http://localhost:3001/api/health

## ✅ **Verificación de Funcionamiento**

### **Backend Health Check**
```bash
curl http://localhost:3001/api/health
```

**Respuesta esperada:**
```json
{
  "status": "healthy",
  "version": "2.0.0",
  "protocol": "2025-03-26",
  "activeSessions": 0,
  "capabilities": {
    "roots": {"listChanged": true},
    "sampling": {},
    "experimental": {}
  }
}
```

### **Prueba de Conexión MCP**
```bash
curl -X POST http://localhost:3001/api/mcp-tools \
  -H "Content-Type: application/json" \
  -d '{
    "path": "npx",
    "args": ["mcp-firebird", "--help"],
    "transport": "stdio"
  }'
```

## 📝 **Notas Importantes**

1. **WebSocket**: Actualmente experimental, puede no funcionar con todos los servidores MCP
2. **SSE**: Recomendado para servidores MCP que soporten HTTP
3. **STDIO**: Mejor para servidores MCP locales como mcp-firebird
4. **Validación**: El formulario ahora valida correctamente todos los tipos de conexión
5. **Guardado**: Las configuraciones se guardan automáticamente en Supabase
6. **Reset**: El formulario se limpia correctamente al cerrar el diálogo

## 🎯 **Configuración Recomendada para MCP Firebird**

Para la mayoría de casos de uso con MCP Firebird, recomendamos:

```
Nombre: Mi Base Firebird
Tipo: STDIO
Path: npx
Arguments: mcp-firebird --database C:\path\to\database.fdb --user SYSDBA --password masterkey
Variables de entorno:
FIREBIRD_HOST=localhost
FIREBIRD_PORT=3050
LOG_LEVEL=info
```

Esta configuración debería funcionar correctamente con el diálogo actualizado.
