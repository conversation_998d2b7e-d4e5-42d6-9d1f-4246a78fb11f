// Vite config con proxy para backend Express
import path from 'node:path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  server: {
    proxy: {
      '/api/mcp-stdio': {
        target: 'http://localhost:3010',
        changeOrigin: true,
        secure: false,
      },
      '/api/mcp-tools': {
        target: 'http://localhost:3010',
        changeOrigin: true,
        secure: false,
      }
    }
  }
});
